import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;
import { ConnectionSystem } from './connectionSystem';
import { simpleToast } from './simpleToast';

/**
 * 无解检测器
 * 
 * 功能：
 * 1. 检测游戏是否处于无解状态
 * 2. 自动触发打乱操作
 * 3. 显示相应的提示信息
 * 4. 统计无解次数和打乱次数
 */
@ccclass('deadlockDetector')
export class deadlockDetector extends Component {
    
    // ==================== 配置参数 ====================
    @property({ displayName: "检测延迟(秒)", tooltip: "消除后延迟多久检测无解状态" })
    detectionDelay: number = 0.5;
    
    @property({ displayName: "启用自动打乱", tooltip: "是否在检测到无解时自动打乱" })
    enableAutoShuffle: boolean = true;
    
    @property({ displayName: "显示提示信息", tooltip: "是否显示无解和打乱的提示信息" })
    showToastMessages: boolean = true;
    
    @property({ displayName: "打乱延迟(秒)", tooltip: "显示无解提示后延迟多久执行打乱" })
    shuffleDelay: number = 1.0;
    
    // ==================== 系统引用 ====================
    private connectionSystem: ConnectionSystem = null!;
    private gameManager: any = null!;  // 游戏管理器引用
    private toastManager: simpleToast = null!;
    
    // ==================== 统计数据 ====================
    private deadlockCount: number = 0;      // 无解次数
    private shuffleCount: number = 0;       // 打乱次数
    private lastCheckTime: number = 0;      // 上次检测时间
    
    // ==================== 状态管理 ====================
    private isChecking: boolean = false;    // 是否正在检测
    private isShuffling: boolean = false;   // 是否正在打乱
    private lastWarningTime: number = 0;    // 上次显示警告的时间
    private readonly WARNING_COOLDOWN = 3000; // 警告冷却时间（3秒）
    
    // ==================== 初始化 ====================
    
    /**
     * 初始化无解检测器
     * @param connectionSystem 连接系统实例
     * @param gameManager 游戏管理器实例
     * @param toastManager Toast管理器实例
     */
    public initialize(connectionSystem: ConnectionSystem, gameManager: any, toastManager: simpleToast): void {
        this.connectionSystem = connectionSystem;
        this.gameManager = gameManager;
        this.toastManager = toastManager;


    }
    
    // ==================== 公共API ====================
    
    /**
     * 检测是否存在可连接的棋子对
     * @param immediate 是否立即检测（跳过延迟）
     */
    public checkForDeadlock(immediate: boolean = false): void {
        if (this.isChecking || this.isShuffling) {
            return;
        }

        if (!this.connectionSystem) {
            console.error("连接系统未初始化，无法进行无解检测");
            return;
        }
        
        const delay = immediate ? 0 : this.detectionDelay;
        
        setTimeout(() => {
            this.performDeadlockCheck();
        }, delay * 1000);
        

    }
    
    /**
     * 强制检测无解状态（同步）
     * @returns 是否处于无解状态
     */
    public isDeadlocked(): boolean {
        if (!this.connectionSystem) {
            return false;
        }
        
        return !this.connectionSystem.hasConnectablePairs();
    }
    
    /**
     * 手动触发打乱
     */
    public forceShuffle(): void {
        if (this.isShuffling) {
            return;
        }
        
        this.performShuffle("手动触发");
    }
    
    /**
     * 获取统计信息
     */
    public getStatistics(): { deadlockCount: number, shuffleCount: number, lastCheckTime: number } {
        return {
            deadlockCount: this.deadlockCount,
            shuffleCount: this.shuffleCount,
            lastCheckTime: this.lastCheckTime
        };
    }
    
    /**
     * 重置统计数据
     */
    public resetStatistics(): void {
        this.deadlockCount = 0;
        this.shuffleCount = 0;
        this.lastCheckTime = 0;

    }
    
    // ==================== 核心检测逻辑 ====================
    
    /**
     * 执行无解检测
     */
    private performDeadlockCheck(): void {
        this.isChecking = true;
        this.lastCheckTime = Date.now();

        // 检查游戏是否已结束（没有棋子了）
        if (this.isGameFinished()) {
            this.isChecking = false;
            return;
        }

        // 检查是否有可连接的棋子对
        const hasConnectablePairs = this.connectionSystem.hasConnectablePairs();

        if (hasConnectablePairs) {
            // 有可连接的棋子，游戏可以继续
            // 移除剩余棋子数量的提示，正常游戏过程中不显示任何提示
        } else {
            // 无可连接的棋子，处于无解状态
            this.handleDeadlockDetected();
        }

        this.isChecking = false;
    }

    /**
     * 检查游戏是否已结束（棋盘上没有棋子了）
     */
    private isGameFinished(): boolean {
        if (!this.connectionSystem || !this.connectionSystem['gridNode']) {
            return false;
        }

        const gridNode = this.connectionSystem['gridNode'];
        if (!gridNode || !gridNode.children) {
            return true;
        }

        // 检查是否还有活跃的棋子（排除路径渲染器节点）
        const activeCells = gridNode.children.filter((cell: any) => cell.active && !cell['isPathRenderer']);
        const isFinished = activeCells.length === 0;



        return isFinished;
    }
    
    /**
     * 处理检测到无解状态
     */
    private handleDeadlockDetected(): void {
        this.deadlockCount++;

        // 检查是否在冷却期内，避免频繁显示警告
        const currentTime = Date.now();
        const shouldShowWarning = this.showToastMessages &&
                                 (currentTime - this.lastWarningTime) > this.WARNING_COOLDOWN;

        // 显示无解提示（带冷却机制）
        if (shouldShowWarning) {
            this.toastManager?.showNoConnectionWarning();
            this.lastWarningTime = currentTime;
        }

        // 如果启用自动打乱，则执行打乱
        if (this.enableAutoShuffle) {
            setTimeout(() => {
                this.performShuffle("无解自动触发");
            }, this.shuffleDelay * 1000);
        }
    }
    
    /**
     * 执行打乱操作
     */
    private performShuffle(reason: string): void {
        if (this.isShuffling) {
            return;
        }

        if (!this.gameManager || typeof this.gameManager.shuffleCells !== 'function') {
            console.error("游戏管理器或洗牌函数未设置");
            return;
        }

        this.isShuffling = true;
        this.shuffleCount++;

        // 显示打乱提示
        if (this.showToastMessages) {
            this.toastManager?.showInfo("正在重新排列棋子...", 1500);
        }

        try {
            // 执行打乱 - 调用游戏管理器的洗牌函数
            this.gameManager.shuffleCells();
            
            // 打乱完成后再次检测
            setTimeout(() => {
                this.isShuffling = false;

                // 验证打乱后是否有可连接的棋子
                this.verifyShuffleResult();
            }, 500);
            
        } catch (error) {
            console.error("打乱操作失败:", error);
            this.isShuffling = false;

            if (this.showToastMessages) {
                this.toastManager?.showError("打乱失败，请手动重新开始");
            }
        }
    }
    
    /**
     * 验证打乱结果
     */
    private verifyShuffleResult(): void {
        const hasConnectablePairs = this.connectionSystem.hasConnectablePairs();
        
        if (hasConnectablePairs) {
            if (this.showToastMessages) {
                this.toastManager?.showShuffleComplete();
            }
        } else {
            console.warn("⚠️ 打乱后仍然无解，可能需要重新打乱");
            
            // 如果打乱后仍然无解，再次尝试打乱（最多3次）
            if (this.shuffleCount % 3 !== 0) {
                setTimeout(() => {
                    this.performShuffle("打乱后验证失败");
                }, 1000);
            } else {
                if (this.showToastMessages) {
                    this.toastManager?.showError("多次打乱仍无解，建议重新开始游戏");
                }
            }
        }
    }
    
    // ==================== 配置方法 ====================
    
    /**
     * 设置检测延迟
     */
    public setDetectionDelay(delay: number): void {
        this.detectionDelay = Math.max(0, delay);
    }

    /**
     * 设置是否启用自动打乱
     */
    public setAutoShuffleEnabled(enabled: boolean): void {
        this.enableAutoShuffle = enabled;
    }

    /**
     * 设置是否显示提示信息
     */
    public setToastMessagesEnabled(enabled: boolean): void {
        this.showToastMessages = enabled;
    }
    
    // ==================== 调试方法 ====================
    
    /**
     * 获取调试信息
     */
    public getDebugInfo(): string {
        const stats = this.getStatistics();
        return `无解检测器: 无解${stats.deadlockCount}次, 打乱${stats.shuffleCount}次, ` +
               `自动打乱${this.enableAutoShuffle ? '启用' : '禁用'}, ` +
               `状态: ${this.isChecking ? '检测中' : this.isShuffling ? '打乱中' : '空闲'}`;
    }
}
