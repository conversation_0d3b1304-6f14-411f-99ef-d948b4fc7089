# Loading预制体设置指南

## 🎯 问题解决

如果游戏一开始加载不出loading预制体，请按照以下步骤检查和设置：

## 📋 检查清单

### 1. **检查gameMgrSimplified组件设置**
- 在场景中找到包含 `gameMgrSimplified` 组件的节点
- 在属性检查器中找到 `Loading Prefab` 属性
- 确保该属性已经设置了loading预制体

### 2. **检查loading预制体结构**
```
Loading预制体
├── 根节点 (添加StartGameUI脚本)
├── 背景图片
├── 游戏标题
├── 开始游戏按钮
└── 其他装饰元素
```

### 3. **检查StartGameUI脚本绑定**
- loading预制体的根节点必须添加 `StartGameUI` 脚本
- 按钮的点击事件必须绑定到 `StartGameUI.startGame` 方法

### 4. **检查按钮事件绑定**
在按钮的Button组件中：
- **Click Events** → 添加事件
- **Target**: loading预制体根节点
- **Component**: StartGameUI
- **Handler**: startGame

## 🔧 设置步骤

### 步骤1：创建Loading预制体
1. **右键项目面板** → Create → Prefab
2. **命名为** `LoadingScreen` 或类似名称
3. **双击进入预制体编辑模式**

### 步骤2：设计Loading界面
1. **添加背景** (Sprite节点)
2. **添加标题** (Label节点)
3. **添加按钮** (Button节点)
4. **调整布局和样式**

### 步骤3：添加StartGameUI脚本
1. **选中预制体根节点**
2. **点击"添加组件"**
3. **搜索并添加 `StartGameUI`**

### 步骤4：绑定按钮事件
1. **选中按钮节点**
2. **在Button组件中找到 Click Events**
3. **点击"+"添加事件**
4. **设置事件**：
   - Target: 拖拽预制体根节点
   - Component: 选择 StartGameUI
   - Handler: 选择 startGame

### 步骤5：保存并设置到游戏管理器
1. **保存预制体** (Ctrl+S)
2. **回到场景**
3. **选中包含gameMgrSimplified的节点**
4. **将loading预制体拖拽到 Loading Prefab 属性**

## 🔍 故障排除

### 问题1：Loading预制体不显示
**可能原因**：
- gameMgrSimplified的Loading Prefab属性未设置
- 预制体路径错误

**解决方案**：
- 检查Loading Prefab属性是否正确设置
- 重新拖拽预制体到属性栏

### 问题2：点击按钮没反应
**可能原因**：
- StartGameUI脚本未添加到预制体
- 按钮事件绑定错误

**解决方案**：
- 确认预制体根节点有StartGameUI组件
- 重新绑定按钮事件

### 问题3：控制台报错"未找到游戏管理器"
**可能原因**：
- 场景中没有gameMgrSimplified组件
- 组件在不同的场景中

**解决方案**：
- 确认当前场景有gameMgrSimplified组件
- 检查组件是否正确添加到节点上

### 问题4：音效不播放
**可能原因**：
- AudioManager未正确初始化
- 音效资源未配置

**解决方案**：
- 检查AudioManager的音效配置
- 确认音效文件路径正确

## 📊 调试信息

运行游戏时，控制台应该显示：
```
🎮 StartGameUI 开始初始化
✅ 找到游戏管理器
🎮 StartGameUI 初始化完成
🎮 Loading界面已显示
```

如果看到：
```
❌ 未找到游戏管理器
```
说明场景中缺少gameMgrSimplified组件。

## ✅ 验证测试

设置完成后测试：
1. **运行游戏** → 应该显示loading界面
2. **点击按钮** → 应该听到音效和感受到震动
3. **界面消失** → loading界面应该自动销毁
4. **游戏开始** → 应该加载游戏主界面

## 🎯 完整流程

```
游戏启动 → gameMgrSimplified.start() → 
显示loading预制体 → StartGameUI初始化 → 
播放等待音效 → 玩家点击按钮 → 
StartGameUI.startGame() → 音效+震动 → 
销毁loading界面 → gameMgrSimplified.startMainGame() → 
加载游戏主界面
```

按照这个指南设置，loading预制体应该能正常显示和工作！
