import { _decorator, Node, Vec3 } from 'cc';
import { gameData } from './gameData';
import { cellFixed } from './cellFixed';

/**
 * 连接检测系统 - 负责连连看的路径连接逻辑
 */
export class ConnectionSystem {
    
    private levelIndex: number;
    private gridNode: Node;
    
    constructor(levelIndex: number, gridNode: Node) {
        this.levelIndex = levelIndex;
        this.gridNode = gridNode;
    }
    
    /**
     * 检查两个棋子是否可以连接
     * 连连看规则：最多允许2个转弯的路径连接
     */
    public canConnect(cell1: Node, cell2: Node): boolean {
        const pos1 = this.getGridPosition(cell1);
        const pos2 = this.getGridPosition(cell2);



        // 优先级1：直线连接（0个转弯）
        if (this.canConnectDirect(pos1, pos2)) {
            return true;
        }

        // 优先级2：一个转弯连接
        if (this.canConnectOneCorner(pos1, pos2)) {
            return true;
        }

        // 优先级3：两个转弯连接
        if (this.canConnectTwoCorner(pos1, pos2)) {
            return true;
        }
        return false;
    }

    /**
     * 获取两个棋子之间的连接路径
     * @param cell1 起始棋子
     * @param cell2 结束棋子
     * @returns 路径点数组（世界坐标），如果无法连接则返回null
     */
    public getConnectionPath(cell1: Node, cell2: Node): Vec3[] | null {
        const pos1 = this.getGridPosition(cell1);
        const pos2 = this.getGridPosition(cell2);

        // 按优先级检查连接并获取路径

        // 优先级1：直线连接
        const directPath = this.getDirectPath(pos1, pos2, cell1, cell2);
        if (directPath) {
            return directPath;
        }

        // 优先级2：一个转弯连接
        const oneCornerPath = this.getOneCornerPath(pos1, pos2, cell1, cell2);
        if (oneCornerPath) {
            return oneCornerPath;
        }

        // 优先级3：两个转弯连接
        const twoCornerPath = this.getTwoCornerPath(pos1, pos2, cell1, cell2);
        if (twoCornerPath) {
            return twoCornerPath;
        }

        return null; // 无法连接
    }
    
    /**
     * 获取棋子在网格中的位置
     */
    private getGridPosition(cellNode: Node): {row: number, col: number} {
        const currentLevel = gameData[this.levelIndex];
        const children = this.gridNode.children;
        const index = children.indexOf(cellNode);
        
        const row = Math.floor(index / currentLevel.col);
        const col = index % currentLevel.col;
        
        return { row, col };
    }
    
    /**
     * 检查指定位置是否为空
     */
    private isEmpty(row: number, col: number, pos1: {row: number, col: number}, pos2: {row: number, col: number}): boolean {
        const currentLevel = gameData[this.levelIndex];
        
        // 边界外的位置视为空
        if (row < 0 || row >= currentLevel.row || col < 0 || col >= currentLevel.col) {
            return true;
        }
        
        // 起始和目标位置不算障碍
        if ((row === pos1.row && col === pos1.col) || (row === pos2.row && col === pos2.col)) {
            return true;
        }
        
        // 检查该位置的棋子是否活跃
        const index = row * currentLevel.col + col;
        const cellNode = this.gridNode.children[index];
        
        return !cellNode || !cellNode.active;
    }
    
    /**
     * 直线连接检查（0个转弯）
     */
    private canConnectDirect(pos1: {row: number, col: number}, pos2: {row: number, col: number}): boolean {
        // 同一行
        if (pos1.row === pos2.row) {
            const minCol = Math.min(pos1.col, pos2.col);
            const maxCol = Math.max(pos1.col, pos2.col);
            for (let col = minCol + 1; col < maxCol; col++) {
                if (!this.isEmpty(pos1.row, col, pos1, pos2)) {
                    return false;
                }
            }
            return true;
        }
        
        // 同一列
        if (pos1.col === pos2.col) {
            const minRow = Math.min(pos1.row, pos2.row);
            const maxRow = Math.max(pos1.row, pos2.row);
            for (let row = minRow + 1; row < maxRow; row++) {
                if (!this.isEmpty(row, pos1.col, pos1, pos2)) {
                    return false;
                }
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * 一个转弯连接检查
     */
    private canConnectOneCorner(pos1: {row: number, col: number}, pos2: {row: number, col: number}): boolean {
        // 检查两个可能的转弯点
        const corner1 = {row: pos1.row, col: pos2.col};
        const corner2 = {row: pos2.row, col: pos1.col};
        
        // 路径1: pos1 -> corner1 -> pos2
        if (this.isEmpty(corner1.row, corner1.col, pos1, pos2)) {
            if (this.canConnectDirect(pos1, corner1) && this.canConnectDirect(corner1, pos2)) {
                return true;
            }
        }
        
        // 路径2: pos1 -> corner2 -> pos2
        if (this.isEmpty(corner2.row, corner2.col, pos1, pos2)) {
            if (this.canConnectDirect(pos1, corner2) && this.canConnectDirect(corner2, pos2)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 两个转弯连接检查
     */
    private canConnectTwoCorner(pos1: {row: number, col: number}, pos2: {row: number, col: number}): boolean {
        const currentLevel = gameData[this.levelIndex];
        
        // 检查同一行的延伸（包括边界外）
        for (let col = -1; col <= currentLevel.col; col++) {
            if (col !== pos1.col && col !== pos2.col) {
                const corner1 = {row: pos1.row, col: col};
                const corner2 = {row: pos2.row, col: col};
                
                if (this.isEmpty(corner1.row, corner1.col, pos1, pos2) &&
                    this.isEmpty(corner2.row, corner2.col, pos1, pos2)) {
                    if (this.canConnectDirect(pos1, corner1) &&
                        this.canConnectDirect(corner1, corner2) &&
                        this.canConnectDirect(corner2, pos2)) {
                        return true;
                    }
                }
            }
        }
        
        // 检查同一列的延伸（包括边界外）
        for (let row = -1; row <= currentLevel.row; row++) {
            if (row !== pos1.row && row !== pos2.row) {
                const corner1 = {row: row, col: pos1.col};
                const corner2 = {row: row, col: pos2.col};
                
                if (this.isEmpty(corner1.row, corner1.col, pos1, pos2) &&
                    this.isEmpty(corner2.row, corner2.col, pos1, pos2)) {
                    if (this.canConnectDirect(pos1, corner1) &&
                        this.canConnectDirect(corner1, corner2) &&
                        this.canConnectDirect(corner2, pos2)) {
                        return true;
                    }
                }
            }
        }
        
        // 检查边界外的四个方向连接
        return this.canConnectThroughBoundary(pos1, pos2);
    }
    
    /**
     * 通过边界外连接检查
     */
    private canConnectThroughBoundary(pos1: {row: number, col: number}, pos2: {row: number, col: number}): boolean {
        // 检查上边界外连接
        if (this.canConnectToEdge(pos1, "top") && this.canConnectToEdge(pos2, "top")) {
            return true;
        }
        
        // 检查下边界外连接
        if (this.canConnectToEdge(pos1, "bottom") && this.canConnectToEdge(pos2, "bottom")) {
            return true;
        }
        
        // 检查左边界外连接
        if (this.canConnectToEdge(pos1, "left") && this.canConnectToEdge(pos2, "left")) {
            return true;
        }
        
        // 检查右边界外连接
        if (this.canConnectToEdge(pos1, "right") && this.canConnectToEdge(pos2, "right")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查棋子是否可以连接到指定边界
     */
    private canConnectToEdge(pos: {row: number, col: number}, edge: string): boolean {
        const currentLevel = gameData[this.levelIndex];
        
        switch (edge) {
            case "top":
                // 检查从当前位置到上边界的路径是否畅通
                for (let row = pos.row - 1; row >= 0; row--) {
                    if (!this.isEmpty(row, pos.col, pos, pos)) {
                        return false;
                    }
                }
                return true;
                
            case "bottom":
                // 检查从当前位置到下边界的路径是否畅通
                for (let row = pos.row + 1; row < currentLevel.row; row++) {
                    if (!this.isEmpty(row, pos.col, pos, pos)) {
                        return false;
                    }
                }
                return true;
                
            case "left":
                // 检查从当前位置到左边界的路径是否畅通
                for (let col = pos.col - 1; col >= 0; col--) {
                    if (!this.isEmpty(pos.row, col, pos, pos)) {
                        return false;
                    }
                }
                return true;
                
            case "right":
                // 检查从当前位置到右边界的路径是否畅通
                for (let col = pos.col + 1; col < currentLevel.col; col++) {
                    if (!this.isEmpty(pos.row, col, pos, pos)) {
                        return false;
                    }
                }
                return true;
                
            default:
                return false;
        }
    }

    /**
     * 查找当前场景中可以连接的棋子对
     * @returns 可连接的棋子对，如果没有则返回null
     */
    public findConnectablePair(): { cell1: Node, cell2: Node } | null {
        if (!this.gridNode || !this.gridNode.children) {
            return null;
        }

        const activeCells = this.gridNode.children.filter((cell: Node) => cell.active && !cell['isPathRenderer']);

        // 如果没有活跃棋子，直接返回null，不输出日志
        if (activeCells.length === 0) {
            return null;
        }

        // 遍历所有活跃的棋子，寻找可连接的对
        for (let i = 0; i < activeCells.length; i++) {
            for (let j = i + 1; j < activeCells.length; j++) {
                const cell1 = activeCells[i];
                const cell2 = activeCells[j];

                // 检查类型是否相同
                const type1 = cell1.getComponent(cellFixed)?.getType();
                const type2 = cell2.getComponent(cellFixed)?.getType();

                if (type1 === type2 && this.canConnect(cell1, cell2)) {
                    return { cell1, cell2 };
                }
            }
        }


        return null;
    }

    /**
     * 检查是否还有可连接的棋子对
     * @returns 是否存在可连接的棋子对
     */
    public hasConnectablePairs(): boolean {
        if (!this.gridNode || !this.gridNode.children) {
            return false;
        }

        const activeCells = this.gridNode.children.filter((cell: Node) => cell.active && !cell['isPathRenderer']);

        // 如果没有活跃棋子，直接返回false
        if (activeCells.length === 0) {
            return false;
        }

        return this.findConnectablePair() !== null;
    }

    /**
     * 获取所有可连接的棋子对
     * @returns 所有可连接的棋子对数组
     */
    public getAllConnectablePairs(): Array<{ cell1: Node, cell2: Node }> {
        if (!this.gridNode || !this.gridNode.children) {
            return [];
        }

        const activeCells = this.gridNode.children.filter((cell: Node) => cell.active && !cell['isPathRenderer']);

        // 如果没有活跃棋子，直接返回空数组，不输出日志
        if (activeCells.length === 0) {
            return [];
        }

        const connectablePairs: Array<{ cell1: Node, cell2: Node }> = [];

        for (let i = 0; i < activeCells.length; i++) {
            for (let j = i + 1; j < activeCells.length; j++) {
                const cell1 = activeCells[i];
                const cell2 = activeCells[j];

                const type1 = cell1.getComponent(cellFixed)?.getType();
                const type2 = cell2.getComponent(cellFixed)?.getType();

                if (type1 === type2 && this.canConnect(cell1, cell2)) {
                    connectablePairs.push({ cell1, cell2 });
                }
            }
        }


        return connectablePairs;
    }

    /**
     * 获取调试信息
     */
    public getDebugInfo(): string {
        const connectablePairs = this.getAllConnectablePairs().length;
        const currentLevel = gameData[this.levelIndex];
        return `连接系统: 关卡${this.levelIndex + 1}, 网格${currentLevel.row}x${currentLevel.col}, 可连接对数: ${connectablePairs}`;
    }

    // ==================== 路径获取方法 ====================

    /**
     * 获取直线连接路径
     */
    private getDirectPath(pos1: {row: number, col: number}, pos2: {row: number, col: number}, cell1: Node, cell2: Node): Vec3[] | null {
        if (!this.canConnectDirect(pos1, pos2)) {
            return null;
        }

        // 直线连接只需要起点和终点
        return [
            cell1.getWorldPosition(),
            cell2.getWorldPosition()
        ];
    }

    /**
     * 获取一个转弯连接路径
     */
    private getOneCornerPath(pos1: {row: number, col: number}, pos2: {row: number, col: number}, cell1: Node, cell2: Node): Vec3[] | null {
        // 检查两个可能的转弯点
        const corner1 = {row: pos1.row, col: pos2.col};
        const corner2 = {row: pos2.row, col: pos1.col};

        // 路径1: pos1 -> corner1 -> pos2
        if (this.isEmpty(corner1.row, corner1.col, pos1, pos2)) {
            if (this.canConnectDirect(pos1, corner1) && this.canConnectDirect(corner1, pos2)) {
                const cornerWorldPos = this.getWorldPositionFromGrid(corner1.row, corner1.col);
                return [
                    cell1.getWorldPosition(),
                    cornerWorldPos,
                    cell2.getWorldPosition()
                ];
            }
        }

        // 路径2: pos1 -> corner2 -> pos2
        if (this.isEmpty(corner2.row, corner2.col, pos1, pos2)) {
            if (this.canConnectDirect(pos1, corner2) && this.canConnectDirect(corner2, pos2)) {
                const cornerWorldPos = this.getWorldPositionFromGrid(corner2.row, corner2.col);
                return [
                    cell1.getWorldPosition(),
                    cornerWorldPos,
                    cell2.getWorldPosition()
                ];
            }
        }

        return null;
    }

    /**
     * 获取两个转弯连接路径
     */
    private getTwoCornerPath(pos1: {row: number, col: number}, pos2: {row: number, col: number}, cell1: Node, cell2: Node): Vec3[] | null {
        const currentLevel = gameData[this.levelIndex];

        // 检查同一行的延伸（包括边界外）
        for (let col = -1; col <= currentLevel.col; col++) {
            if (col !== pos1.col && col !== pos2.col) {
                const corner1 = {row: pos1.row, col: col};
                const corner2 = {row: pos2.row, col: col};

                if (this.isEmpty(corner1.row, corner1.col, pos1, pos2) &&
                    this.isEmpty(corner2.row, corner2.col, pos1, pos2)) {
                    if (this.canConnectDirect(pos1, corner1) &&
                        this.canConnectDirect(corner1, corner2) &&
                        this.canConnectDirect(corner2, pos2)) {

                        const corner1WorldPos = this.getWorldPositionFromGrid(corner1.row, corner1.col);
                        const corner2WorldPos = this.getWorldPositionFromGrid(corner2.row, corner2.col);

                        return [
                            cell1.getWorldPosition(),
                            corner1WorldPos,
                            corner2WorldPos,
                            cell2.getWorldPosition()
                        ];
                    }
                }
            }
        }

        // 检查同一列的延伸（包括边界外）
        for (let row = -1; row <= currentLevel.row; row++) {
            if (row !== pos1.row && row !== pos2.row) {
                const corner1 = {row: row, col: pos1.col};
                const corner2 = {row: row, col: pos2.col};

                if (this.isEmpty(corner1.row, corner1.col, pos1, pos2) &&
                    this.isEmpty(corner2.row, corner2.col, pos1, pos2)) {
                    if (this.canConnectDirect(pos1, corner1) &&
                        this.canConnectDirect(corner1, corner2) &&
                        this.canConnectDirect(corner2, pos2)) {

                        const corner1WorldPos = this.getWorldPositionFromGrid(corner1.row, corner1.col);
                        const corner2WorldPos = this.getWorldPositionFromGrid(corner2.row, corner2.col);

                        return [
                            cell1.getWorldPosition(),
                            corner1WorldPos,
                            corner2WorldPos,
                            cell2.getWorldPosition()
                        ];
                    }
                }
            }
        }

        // 检查边界外连接路径
        return this.getBoundaryConnectionPath(pos1, pos2, cell1, cell2);
    }

    /**
     * 获取边界外连接路径
     */
    private getBoundaryConnectionPath(pos1: {row: number, col: number}, pos2: {row: number, col: number}, cell1: Node, cell2: Node): Vec3[] | null {
        const currentLevel = gameData[this.levelIndex];

        // 检查上边界外连接
        if (this.canConnectToEdge(pos1, "top") && this.canConnectToEdge(pos2, "top")) {
            const topY = this.getWorldPositionFromGrid(-1, 0).y;
            const corner1 = new Vec3(cell1.getWorldPosition().x, topY, 0);
            const corner2 = new Vec3(cell2.getWorldPosition().x, topY, 0);

            return [
                cell1.getWorldPosition(),
                corner1,
                corner2,
                cell2.getWorldPosition()
            ];
        }

        // 检查下边界外连接
        if (this.canConnectToEdge(pos1, "bottom") && this.canConnectToEdge(pos2, "bottom")) {
            const bottomY = this.getWorldPositionFromGrid(currentLevel.row, 0).y;
            const corner1 = new Vec3(cell1.getWorldPosition().x, bottomY, 0);
            const corner2 = new Vec3(cell2.getWorldPosition().x, bottomY, 0);

            return [
                cell1.getWorldPosition(),
                corner1,
                corner2,
                cell2.getWorldPosition()
            ];
        }

        // 检查左边界外连接
        if (this.canConnectToEdge(pos1, "left") && this.canConnectToEdge(pos2, "left")) {
            const leftX = this.getWorldPositionFromGrid(0, -1).x;
            const corner1 = new Vec3(leftX, cell1.getWorldPosition().y, 0);
            const corner2 = new Vec3(leftX, cell2.getWorldPosition().y, 0);

            return [
                cell1.getWorldPosition(),
                corner1,
                corner2,
                cell2.getWorldPosition()
            ];
        }

        // 检查右边界外连接
        if (this.canConnectToEdge(pos1, "right") && this.canConnectToEdge(pos2, "right")) {
            const rightX = this.getWorldPositionFromGrid(0, currentLevel.col).x;
            const corner1 = new Vec3(rightX, cell1.getWorldPosition().y, 0);
            const corner2 = new Vec3(rightX, cell2.getWorldPosition().y, 0);

            return [
                cell1.getWorldPosition(),
                corner1,
                corner2,
                cell2.getWorldPosition()
            ];
        }

        return null;
    }

    /**
     * 根据网格坐标获取世界坐标
     */
    private getWorldPositionFromGrid(row: number, col: number): Vec3 {
        const currentLevel = gameData[this.levelIndex];

        // 处理边界外的情况
        if (row < 0 || row >= currentLevel.row || col < 0 || col >= currentLevel.col) {
            // 对于边界外的点，我们需要推算其位置
            const cellSpacing = 75; // 从config.ts获取
            const firstCellPos = this.gridNode.children[0]?.getWorldPosition() || new Vec3(0, 0, 0);

            return new Vec3(
                firstCellPos.x + col * cellSpacing,
                firstCellPos.y - row * cellSpacing,
                0
            );
        }

        // 正常网格内的位置
        const index = row * currentLevel.col + col;
        const cellNode = this.gridNode.children[index];

        if (cellNode) {
            return cellNode.getWorldPosition();
        }

        // 如果节点不存在，推算位置
        const cellSpacing = 75;
        const firstCellPos = this.gridNode.children[0]?.getWorldPosition() || new Vec3(0, 0, 0);

        return new Vec3(
            firstCellPos.x + col * cellSpacing,
            firstCellPos.y - row * cellSpacing,
            0
        );
    }
}
