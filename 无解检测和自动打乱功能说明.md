# 无解检测和自动打乱功能说明

## 🎯 功能概述

为连连看游戏添加了智能的无解检测和自动打乱系统，确保游戏始终处于可玩状态，提升用户体验。

## 🔧 核心功能

### 1. **自动无解检测**
- **游戏初始化后**：检测初始棋盘是否有可连接的棋子
- **每次消除后**：检测剩余棋子是否还有可连接的组合
- **智能延迟**：避免频繁检测，优化性能

### 2. **自动打乱机制**
- **无解时触发**：检测到无解状态时自动重新排列棋子
- **多次验证**：打乱后验证结果，确保有可连接的棋子
- **失败重试**：如果打乱后仍无解，最多重试3次

### 3. **美观的提示系统**
- **Toast提示框**：支持多种类型和样式的提示
- **动画效果**：平滑的显示和隐藏动画
- **自动消失**：提示信息自动消失，不影响游戏

## 🏗️ 系统架构

### 1. **uiToastManager** - 通用提示框系统

#### 功能特性：
- **多种类型**：信息、成功、警告、错误四种类型
- **自定义样式**：不同颜色和图标
- **动画效果**：缩放和透明度动画
- **位置控制**：支持顶部、中部、底部显示

#### 使用方法：
```typescript
// 静态方法调用
uiToastManager.showInfo("信息提示");
uiToastManager.showSuccess("操作成功");
uiToastManager.showWarning("警告信息");
uiToastManager.showError("错误信息");

// 自定义配置
uiToastManager.show({
    message: "自定义提示",
    type: ToastType.INFO,
    duration: 3000,
    position: 'center'
});
```

### 2. **deadlockDetector** - 无解检测器

#### 核心算法：
```typescript
// 检测是否有可连接的棋子对
public checkForDeadlock(immediate: boolean = false): void {
    // 延迟检测，避免频繁调用
    this.scheduleOnce(() => {
        const hasConnectablePairs = this.connectionSystem.hasConnectablePairs();
        if (!hasConnectablePairs) {
            this.handleDeadlockDetected();
        }
    }, this.detectionDelay);
}
```

#### 配置参数：
- **检测延迟**：消除后延迟多久检测（默认0.5秒）
- **自动打乱**：是否启用自动打乱（默认启用）
- **提示信息**：是否显示提示信息（默认启用）
- **打乱延迟**：显示提示后延迟多久打乱（默认1秒）

### 3. **集成到游戏管理器**

#### 初始化流程：
```typescript
// 1. 初始化系统
this.deadlockDetector = this.node.addComponent(deadlockDetector);

// 2. 设置回调
this.deadlockDetector.initialize(this.connectionSystem, () => {
    this.shuffleCells();
});

// 3. 游戏开始后检测
this.deadlockDetector.checkForDeadlock(true);
```

#### 消除后检测：
```typescript
onCellClick(cellNode: Node) {
    this.selectionLogicManager.handleCellClick(cellNode, () => {
        this.checkGameWin();
        // 每次消除后检测无解状态
        this.deadlockDetector.checkForDeadlock();
    });
}
```

## 🎬 用户体验流程

### 场景1：游戏初始化
```
游戏开始 → 棋盘生成 → 延迟0.5秒 → 检测可连接性
├─ 有可连接棋子 → 游戏正常开始
└─ 无可连接棋子 → 显示"无可连接棋子，即将自动打乱" → 1秒后打乱 → 显示"重新排列完成！"
```

### 场景2：游戏进行中
```
用户消除棋子 → 延迟0.5秒 → 检测剩余棋子可连接性
├─ 有可连接棋子 → 游戏继续
│   └─ 可连接对≤2 → 显示"剩余X对可连接棋子"（警告）
└─ 无可连接棋子 → 显示"无可连接棋子，即将自动打乱" → 1秒后打乱 → 显示"重新排列完成！"
```

### 场景3：手动提示
```
用户点击提示按钮
├─ 有可连接棋子 → 高亮显示 → 显示"已为您高亮可连接的棋子"
└─ 无可连接棋子 → 显示"无可连接棋子，即将自动重排" → 立即打乱
```

## 🎨 Toast提示框样式

### 提示类型和颜色：
- **信息提示** (INFO)：蓝色 `#3498db` + ℹ️ 图标
- **成功提示** (SUCCESS)：绿色 `#2ecc71` + ✅ 图标
- **警告提示** (WARNING)：橙色 `#f1c40f` + ⚠️ 图标
- **错误提示** (ERROR)：红色 `#e74c3c` + ❌ 图标

### 动画效果：
```typescript
// 显示动画：缩放 + 透明度
显示: scale(0.8 → 1.0) + opacity(0 → 255)
隐藏: scale(1.0 → 0.8) + opacity(255 → 0)
动画时长: 0.3秒
```

### 显示位置：
- **顶部** (top)：容器高度的30%位置
- **中部** (center)：容器中心位置
- **底部** (bottom)：容器高度的-30%位置

## 📊 统计和监控

### 统计数据：
```typescript
interface Statistics {
    deadlockCount: number;    // 无解次数
    shuffleCount: number;     // 打乱次数
    lastCheckTime: number;    // 上次检测时间
}
```

### 调试信息：
```typescript
// 获取检测器状态
deadlockDetector.getDebugInfo();
// 输出：无解检测器: 无解2次, 打乱2次, 自动打乱启用, 状态: 空闲

// 获取连接系统状态
connectionSystem.getDebugInfo();
// 输出：连接系统: 关卡1, 网格4x4, 可连接对数: 3
```

## 🔧 配置选项

### 无解检测器配置：
```typescript
// 设置检测延迟
deadlockDetector.setDetectionDelay(0.8);

// 启用/禁用自动打乱
deadlockDetector.setAutoShuffleEnabled(false);

// 启用/禁用提示信息
deadlockDetector.setToastMessagesEnabled(true);
```

### Toast管理器配置：
```typescript
// 自定义显示时长和位置
uiToastManager.show({
    message: "自定义提示",
    type: ToastType.WARNING,
    duration: 5000,        // 5秒
    position: 'top',       // 顶部显示
    showIcon: false        // 不显示图标
});
```

## 🛠️ 实现细节

### 1. **性能优化**
- **延迟检测**：避免在动画过程中频繁检测
- **缓存结果**：连接系统缓存可连接对的计算结果
- **异步处理**：使用 `scheduleOnce` 避免阻塞主线程

### 2. **错误处理**
- **多次重试**：打乱失败时最多重试3次
- **降级处理**：多次打乱仍无解时提示用户重新开始
- **异常捕获**：捕获打乱过程中的异常

### 3. **状态管理**
- **防重入**：检测和打乱过程中防止重复调用
- **状态跟踪**：记录当前是否在检测或打乱中
- **统计信息**：记录无解和打乱的次数

## 📋 使用指南

### 1. **基本设置**
1. 在场景中添加 `uiToastManager` 组件
2. 设置Toast容器和模板（可选）
3. 游戏管理器会自动初始化无解检测器

### 2. **自定义配置**
```typescript
// 在游戏管理器中配置
this.deadlockDetector.setDetectionDelay(1.0);      // 1秒后检测
this.deadlockDetector.setAutoShuffleEnabled(true); // 启用自动打乱
this.deadlockDetector.setToastMessagesEnabled(true); // 显示提示
```

### 3. **手动控制**
```typescript
// 手动检测无解
this.deadlockDetector.checkForDeadlock(true);

// 强制打乱
this.deadlockDetector.forceShuffle();

// 获取统计信息
const stats = this.deadlockDetector.getStatistics();
```

## ✅ 功能验证

### 测试场景：
1. **初始化测试**：创建无解的初始棋盘，验证自动打乱
2. **消除后测试**：消除到只剩无法连接的棋子，验证检测和打乱
3. **提示按钮测试**：在无解状态下点击提示，验证提示信息
4. **多次打乱测试**：验证打乱失败时的重试机制
5. **性能测试**：验证检测延迟和动画流畅性

### 预期结果：
- ✅ 无解状态被正确检测
- ✅ 自动打乱功能正常工作
- ✅ 提示信息美观且及时
- ✅ 用户体验流畅自然
- ✅ 性能表现良好

通过这套完整的无解检测和自动打乱系统，连连看游戏能够智能地保持可玩状态，大大提升了用户体验！
