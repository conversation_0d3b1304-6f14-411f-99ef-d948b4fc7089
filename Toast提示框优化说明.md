# Toast提示框优化说明

## 🎯 优化目标

解决Toast提示框的美观度和重叠问题，提升用户体验。

## 🔧 主要优化内容

### 1. **位置优化**
- **更高的显示位置**：从 Y=100 提升到 Y=200，更靠近屏幕上方
- **智能间距**：多个Toast之间间距80像素，避免重叠
- **动态排列**：新Toast出现时，旧Toast自动下移

### 2. **队列管理系统**
```typescript
// 队列配置
private activeToasts: Node[] = [];           // 活跃Toast队列
private readonly MAX_TOASTS = 3;             // 最大同时显示3个
private readonly TOAST_SPACING = 80;        // Toast间距80像素
private readonly BASE_Y_POSITION = 200;     // 基础Y位置
```

#### 队列工作流程：
```
新Toast请求 → 检查重复 → 检查队列限制 → 移除超出的Toast → 添加新Toast → 重新排列位置
```

### 3. **防重复机制**
```typescript
// 重复检查
private isDuplicateToast(message: string): boolean {
    return this.activeToasts.some(toast => {
        return toast && toast.isValid && toast['_toastMessage'] === message;
    });
}
```

#### 防重复特性：
- **内容检查**：相同消息内容的Toast不会重复显示
- **时间冷却**：无解警告有3秒冷却期，避免频繁提示
- **智能跳过**：重复Toast请求会被自动跳过

### 4. **美观度提升**

#### 视觉样式：
```typescript
// 更丰富的颜色配置
private readonly COLORS = {
    info: new Color(52, 152, 219, 255),      // 蓝色文字
    success: new Color(46, 204, 113, 255),   // 绿色文字
    warning: new Color(241, 196, 15, 255),   // 橙色文字
    error: new Color(231, 76, 60, 255)       // 红色文字
};

private readonly BACKGROUND_COLORS = {
    info: new Color(52, 152, 219, 40),       // 半透明蓝色背景
    success: new Color(46, 204, 113, 40),    // 半透明绿色背景
    warning: new Color(241, 196, 15, 40),    // 半透明橙色背景
    error: new Color(231, 76, 60, 40)        // 半透明红色背景
};
```

#### 背景设计：
- **半透明背景**：300x50像素的圆角背景
- **颜色呼应**：背景色与文字色相呼应
- **层次分明**：背景在文字后面，增加可读性

### 5. **平滑动画**

#### 显示动画：
```typescript
// 缩放 + 透明度同时进行
显示: scale(0.8 → 1.0) + opacity(0 → 255)
缓动: backOut（回弹效果）
时长: 0.3秒
```

#### 位置调整动画：
```typescript
// Toast移除后，剩余Toast平滑移动到新位置
tween(toast)
    .to(0.3, { position: new Vec3(0, targetY, 0) })
    .start();
```

## 🔄 工作流程优化

### 1. **Toast生命周期**
```
创建请求 → 重复检查 → 队列管理 → 位置计算 → 显示动画 → 延迟等待 → 隐藏动画 → 队列移除 → 位置重排
```

### 2. **无解检测优化**
```typescript
// 添加冷却机制
private lastWarningTime: number = 0;
private readonly WARNING_COOLDOWN = 3000; // 3秒冷却

// 检查冷却期
const shouldShowWarning = this.showToastMessages && 
                         (currentTime - this.lastWarningTime) > this.WARNING_COOLDOWN;
```

### 3. **队列限制处理**
```typescript
// 超出限制时移除最旧的Toast
private manageToastQueue(): void {
    while (this.activeToasts.length >= this.MAX_TOASTS) {
        const oldestToast = this.activeToasts.shift();
        // 立即隐藏最旧的Toast
        this.playHideAnimation(oldestToast, () => {
            oldestToast.destroy();
        });
    }
}
```

## 🎨 视觉效果

### Toast样式：
```
┌─────────────────────────────────┐
│  ℹ️ 已为您高亮可连接的棋子        │  ← 信息Toast（蓝色）
└─────────────────────────────────┘
           ↓ 80px间距
┌─────────────────────────────────┐
│  ⚠️ 无可连接棋子，即将自动打乱    │  ← 警告Toast（橙色）
└─────────────────────────────────┘
           ↓ 80px间距
┌─────────────────────────────────┐
│  ✅ 重新排列完成！               │  ← 成功Toast（绿色）
└─────────────────────────────────┘
```

### 位置布局：
- **基础位置**：Y = 200（屏幕中央偏上）
- **第二个Toast**：Y = 120（200 - 80）
- **第三个Toast**：Y = 40（120 - 80）
- **超出限制**：自动移除最旧的Toast

## 📊 性能优化

### 1. **内存管理**
- **及时清理**：Toast显示完毕后立即销毁节点
- **队列限制**：最多同时存在3个Toast，避免内存堆积
- **引用清理**：从队列中移除时清理所有引用

### 2. **动画优化**
- **硬件加速**：使用tween动画，利用GPU加速
- **批量处理**：位置重排时批量更新，减少重绘
- **智能跳过**：重复Toast直接跳过，避免无效计算

### 3. **事件优化**
- **防抖机制**：无解检测有冷却期，避免频繁触发
- **异步处理**：使用setTimeout避免阻塞主线程
- **条件检查**：多重条件检查，减少不必要的操作

## 🛠️ 使用场景

### 1. **游戏初始化**
```
游戏开始 → 检测无解 → 显示警告Toast(Y=200) → 自动打乱 → 显示成功Toast(Y=120)
```

### 2. **连续消除**
```
消除棋子1 → 检测剩余 → 显示警告Toast(Y=200)
消除棋子2 → 检测剩余 → 跳过重复警告（冷却期内）
自动打乱完成 → 显示成功Toast(Y=120)
```

### 3. **提示功能**
```
点击提示按钮 → 有可连接 → 显示信息Toast(Y=200)
再次点击提示 → 跳过重复提示
```

## ✅ 优化效果

### 解决的问题：
- ✅ **重叠问题**：Toast按队列排列，间距80像素，不再重叠
- ✅ **位置问题**：提升到Y=200，更靠近屏幕上方，更显眼
- ✅ **重复问题**：智能重复检查和冷却机制，避免频繁提示
- ✅ **美观问题**：半透明背景、更好的颜色搭配、平滑动画

### 提升的体验：
- 🎯 **视觉层次**：不同类型Toast有明确的颜色区分
- 🎯 **动画流畅**：平滑的显示、隐藏、位置调整动画
- 🎯 **信息清晰**：避免重复提示，信息更有价值
- 🎯 **性能稳定**：队列管理确保内存使用可控

## 🔧 配置参数

可以通过修改以下参数来调整Toast行为：

```typescript
// 队列配置
private readonly MAX_TOASTS = 3;        // 最大Toast数量
private readonly TOAST_SPACING = 80;   // Toast间距
private readonly BASE_Y_POSITION = 200; // 基础Y位置

// 冷却配置
private readonly WARNING_COOLDOWN = 3000; // 警告冷却时间（毫秒）

// 样式配置
label.fontSize = 24;                    // 字体大小
uiTransform.setContentSize(300, 50);   // 背景尺寸
```

通过这些优化，Toast提示系统现在能够提供更美观、更智能、更流畅的用户体验！
