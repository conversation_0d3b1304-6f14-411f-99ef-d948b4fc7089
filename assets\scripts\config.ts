//设备宽高
export const deviceWidth: number = 720;
export const deviceHeight: number = 1280;

// //棋盘宽高
// export const gridWidth: number = 700;
// export const gridHeight: number = 700;

//棋子间距（控制棋子之间的距离）
export const cellSpacing: number = 75;

//棋子默认大小（可在编辑器中覆盖）
export const defaultCellSize: number = 100;

//水果种类最小值
export const minFruitType: number = 1;
//水果种类最大值
export const maxFruitType: number = 11;

//关卡数=第几关
export const levelNum: number =3;

// ==================== 日志控制配置 ====================

/**
 * 日志控制开关
 * 设置为 false 可以关闭对应模块的日志输出
 */
export const LogConfig = {
    // 游戏核心模块 - 细化的游戏管理器日志开关
    gameManager: true,           // 游戏管理器总开关（启用，显示关键业务流程）
    gameManagerLifecycle: true,  // 生命周期日志（启动、进入游戏等）
    gameManagerSystem: true,     // 系统初始化日志（音频、振动、Toast等）
    gameManagerGrid: true,       // 棋盘相关日志（初始化、洗牌、创建等）
    gameManagerClick: false,     // 棋子点击日志（关闭，频繁输出）
    gameManagerWin: true,        // 游戏胜利日志（重要事件）
    gameManagerProgressive: false, // 渐进式展现日志（关闭，输出太多）
    gameManagerTest: false,      // 测试功能日志（仅开发时需要）

    connectionSystem: false,     // 连接系统日志（关闭，输出太多）
    selectionLogic: false,       // 选择逻辑日志（关闭，输出太多）
    animationState: false,       // 动画状态日志（关闭，输出太多）

    // 检测和算法模块 - 保留重要提示
    deadlockDetector: true,      // 无解检测日志（保留，重要提示）
    shuffleAlgorithm: false,     // 洗牌算法详细日志（关闭，太详细）

    // UI和交互模块 - 启用用户操作日志
    menuManager: true,           // 菜单管理器日志（启用，显示用户操作）
    toastSystem: true,           // Toast提示系统日志（保留，用户可见）

    // 音频模块 - 启用音频状态日志
    audioManager: false,          // 音频管理器日志（启用，显示音频状态）

    // 界面和组件模块 - 启用界面切换日志
    loading: false,               // 加载界面日志（保留，重要状态）
    cellComponent: false,        // 棋子组件日志（关闭，输出太多）

    // 系统模块 - 启用系统服务日志
    vibration: false,             // 振动系统日志（启用，显示系统状态）

    // 全局开关
    enableAllLogs: false,        // 设置为 true 时启用所有日志
    enableErrorLogs: true,       // 错误日志始终启用（重要！）
};