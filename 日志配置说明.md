# 连连看游戏 - 日志配置说明

## 📋 当前日志配置（简化版）

### ✅ 启用的日志模块（仅保留重要信息）

1. **deadlockDetector: true** - 无解检测日志
   - 输出内容：检测到无解状态时的提示
   - 重要性：⭐⭐⭐⭐⭐ 游戏核心功能，必须保留
   - 示例：`🔍 [Deadlock] 检测到无解状态，开始洗牌`

2. **toastSystem: true** - Toast提示系统日志
   - 输出内容：用户可见的提示信息
   - 重要性：⭐⭐⭐⭐ 用户体验相关
   - 示例：`🍞 [Toast] 显示提示: 恭喜过关！`

3. **loading: true** - 加载界面日志
   - 输出内容：游戏加载状态信息
   - 重要性：⭐⭐⭐⭐ 帮助调试加载问题
   - 示例：`📦 [Loading] 游戏资源加载完成`

4. **enableErrorLogs: true** - 错误日志（始终启用）
   - 输出内容：所有模块的错误信息
   - 重要性：⭐⭐⭐⭐⭐ 必须保留，用于问题排查
   - 示例：`🎮 [GameManager] ERROR: 棋子创建失败`

### ❌ 禁用的日志模块（减少噪音）

1. **gameManager: false** - 游戏管理器日志
   - 原因：输出过多，影响调试效率

2. **connectionSystem: false** - 连接系统日志
   - 原因：每次连接检测都会输出，信息量巨大

3. **selectionLogic: false** - 选择逻辑日志
   - 原因：每次点击都会输出，过于频繁

4. **animationState: false** - 动画状态日志
   - 原因：动画过程中输出过多

5. **shuffleAlgorithm: false** - 洗牌算法详细日志
   - 原因：算法细节对用户不重要

6. **menuManager: false** - 菜单管理器日志
   - 原因：UI操作日志不是核心功能

7. **audioManager: false** - 音频管理器日志
   - 原因：音频操作日志不是核心功能

8. **cellComponent: false** - 棋子组件日志
   - 原因：每个棋子操作都会输出，过于频繁

9. **testing: false** - 测试模块日志
   - 原因：仅在开发测试时需要

10. **vibration: false** - 振动系统日志
    - 原因：振动功能日志不是核心功能

## 🔧 如何调整日志配置

### 临时启用某个模块的日志
在 `assets/scripts/config.ts` 文件中，将对应模块设置为 `true`：

```typescript
export const LogConfig = {
    gameManager: true,  // 临时启用游戏管理器日志
    // ... 其他配置
};
```

### 启用所有日志（调试模式）
```typescript
export const LogConfig = {
    enableAllLogs: true,  // 启用所有日志
    // ... 其他配置
};
```

### 完全静默模式（生产环境）
```typescript
export const LogConfig = {
    enableAllLogs: false,
    enableErrorLogs: false,  // 连错误日志也关闭
    // 所有其他模块设为 false
};
```

## 📊 日志输出示例

### 正常游戏流程中的日志输出
```
📦 [Loading] 游戏资源加载完成
🔍 [Deadlock] 检测到无解状态，开始洗牌
🍞 [Toast] 显示提示: 恭喜过关！
🔍 [Deadlock] 检测到无解状态，开始洗牌
🍞 [Toast] 显示提示: 游戏结束！
```

### 出现错误时的日志输出
```
📦 [Loading] 游戏资源加载完成
🎮 [GameManager] ERROR: 棋子创建失败
🔗 [Connection] ERROR: 连接路径计算异常
```

## 💡 建议

1. **日常开发**：使用当前配置即可，只看重要信息
2. **调试特定功能**：临时启用相关模块的日志
3. **性能测试**：关闭所有日志，避免影响性能
4. **生产发布**：只保留 `enableErrorLogs: true`

## 🚀 快速切换配置

在 `config.ts` 文件顶部添加预设配置：

```typescript
// 开发模式：只显示重要信息
const DEVELOPMENT_MODE = {
    deadlockDetector: true,
    toastSystem: true,
    loading: true,
    enableErrorLogs: true,
    // 其他全部 false
};

// 调试模式：显示更多信息
const DEBUG_MODE = {
    enableAllLogs: true,
};

// 生产模式：只显示错误
const PRODUCTION_MODE = {
    enableErrorLogs: true,
    // 其他全部 false
};

// 当前使用的配置
export const LogConfig = DEVELOPMENT_MODE;
```
