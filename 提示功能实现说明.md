# 提示功能实现说明

## 🎯 功能概述

为连连看游戏添加了提示功能，点击提示按钮后会高亮显示一对当前场景中可以连接的棋子，帮助用户找到可行的连接路径。

## 🔧 实现架构

### 1. **ConnectionSystem 扩展**
添加了查找可连接棋子对的核心算法：

```typescript
// 查找单个可连接的棋子对
public findConnectablePair(): { cell1: Node, cell2: Node } | null

// 检查是否还有可连接的棋子对
public hasConnectablePairs(): boolean

// 获取所有可连接的棋子对
public getAllConnectablePairs(): Array<{ cell1: Node, cell2: Node }>
```

**算法逻辑**：
1. 遍历所有活跃的棋子
2. 检查每两个棋子的类型是否相同
3. 使用现有的连接检测算法验证是否可连接
4. 返回第一个找到的可连接对

### 2. **AnimationStateManager 扩展**
添加了新的提示动画状态和相关方法：

```typescript
// 新增动画状态
HINT = "hint"  // 提示状态：高亮闪烁，提示可连接的棋子

// 新增方法
public playHintAnimation(cellNode: Node, callback?: () => void): void
public clearHintStates(): void
```

**提示动画效果**：
- 棋子进行3次高亮闪烁（1.0 ↔ 1.2倍缩放）
- 总时长1.5秒，给用户足够的提示时间
- 动画完成后自动恢复正常状态

### 3. **SelectionLogicManager 扩展**
添加了提示功能的业务逻辑：

```typescript
// 显示提示
public showHint(): boolean

// 检查可连接对
public hasConnectablePairs(): boolean

// 获取可连接对数量
public getConnectablePairsCount(): number
```

**提示逻辑**：
1. 清理之前的提示状态
2. 查找可连接的棋子对
3. 播放提示动画
4. 返回是否成功显示提示

### 4. **MenuManager 扩展**
添加了提示按钮的UI控制：

```typescript
@property({ type: Button, displayName: "提示按钮" })
hintButton: Button = null!;

private onHintClick(): void
```

## 🎬 动画效果详解

### 提示动画序列：
```typescript
// 第一次闪烁：正常 → 放大 → 正常
.to(0.2, { scale: hintScale })     // 放大到1.2倍
.to(0.2, { scale: originalScale }) // 恢复原始大小

// 第二次闪烁：正常 → 放大 → 正常
.to(0.2, { scale: hintScale })
.to(0.2, { scale: originalScale })

// 第三次闪烁：正常 → 放大 → 正常
.to(0.2, { scale: hintScale })
.to(0.2, { scale: originalScale })

// 最终恢复正常状态
.to(0.3, { scale: this.NORMAL_SCALE })
```

### 动画特点：
- **醒目**：1.2倍缩放，明显的视觉效果
- **节奏感**：3次闪烁，有规律的节奏
- **不干扰**：自动恢复，不影响游戏进行

## 🔄 状态管理

### 提示状态的生命周期：
1. **触发**：用户点击提示按钮
2. **显示**：棋子开始闪烁动画
3. **清理**：用户点击任意棋子或动画自然结束

### 状态清理机制：
```typescript
// 用户点击时自动清理提示状态
public handleCellClick(): void {
    this.animationManager.clearHintStates();  // 清理提示
    // ... 其他点击逻辑
}

// 专门的提示状态清理
public clearHintStates(): void {
    // 找到所有提示状态的棋子
    // 停止动画并重置到正常状态
}
```

## 📋 使用指南

### 1. **在预制体中设置提示按钮**
1. 打开菜单预制体编辑器
2. 在 `menuManager` 组件中设置 `hintButton` 属性
3. 将提示按钮节点拖拽到该属性槽

### 2. **按钮功能**
- **点击提示按钮** → 高亮显示一对可连接的棋子
- **如果没有可连接的棋子** → 控制台输出提示信息
- **用户点击任意棋子** → 自动清除提示效果

### 3. **调试信息**
```typescript
// 查看连接系统状态
console.log(connectionSystem.getDebugInfo());
// 输出：连接系统: 关卡1, 网格4x4, 可连接对数: 3

// 查看选择管理器状态
console.log(selectionLogicManager.getDebugInfo());
// 输出：选择状态: 无选中, 类型: 无, 可连接对数: 3
```

## 🛠️ 技术特性

### 1. **智能查找算法**
- **高效**：O(n²)时间复杂度，对于连连看的棋子数量完全可接受
- **准确**：使用现有的连接检测算法，确保提示的准确性
- **灵活**：支持所有连接模式（直线、一转弯、二转弯）

### 2. **动画防冲突**
- **ID机制**：每个动画都有唯一ID，防止冲突
- **状态管理**：完整的状态跟踪和清理机制
- **优雅降级**：动画被中断时正确恢复状态

### 3. **用户体验优化**
- **即时反馈**：点击后立即显示提示
- **自动清理**：用户操作后自动清除提示
- **视觉友好**：适中的闪烁频率，不会造成视觉疲劳

## 🔍 算法优化

### 查找策略：
1. **优先级**：返回第一个找到的可连接对，不进行复杂排序
2. **缓存**：可以考虑缓存结果，但当前实现为实时计算
3. **过滤**：只检查活跃的棋子，跳过已消除的棋子

### 性能考虑：
- **时间复杂度**：O(n²)，n为活跃棋子数量
- **空间复杂度**：O(1)，只存储当前检查的棋子对
- **实际性能**：对于典型的连连看游戏（16-64个棋子），性能完全可接受

## 🚀 扩展功能建议

### 1. **多级提示**
```typescript
// 可以扩展为显示多个可连接对
public showMultipleHints(count: number = 3): boolean

// 或者按难度显示提示
public showHintByDifficulty(difficulty: 'easy' | 'medium' | 'hard'): boolean
```

### 2. **提示计数限制**
```typescript
// 限制提示使用次数
private hintCount: number = 3;
public canUseHint(): boolean { return this.hintCount > 0; }
```

### 3. **智能提示**
```typescript
// 优先显示即将无解的区域
public showSmartHint(): boolean

// 显示最短路径的连接
public showShortestPathHint(): boolean
```

## ✅ 功能验证

### 测试场景：
1. **基本功能**：点击提示按钮，验证是否正确高亮可连接的棋子
2. **无解情况**：在没有可连接棋子时点击提示，验证提示信息
3. **状态清理**：显示提示后点击棋子，验证提示是否正确清除
4. **动画效果**：验证提示动画的视觉效果和时长
5. **性能测试**：在不同棋子数量下测试查找性能

### 预期结果：
- ✅ 提示功能正常工作
- ✅ 动画效果流畅自然
- ✅ 状态管理正确无误
- ✅ 性能表现良好
- ✅ 用户体验友好

通过这个提示功能，玩家在遇到困难时可以获得有效的帮助，提升游戏的可玩性和用户体验！
