import { _decorator, Component, Node, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;
import { LogManager } from './logManager';

/**
 * 动画系统 - 负责游戏中所有动画效果
 */
@ccclass('AnimationSystem')
export class AnimationSystem {
    
    /**
     * 选中棋子的动画效果
     * @param cellNode 要选中的棋子节点
     */
    public static playSelectAnimation(cellNode: Node): void {
        if (!cellNode) return;
        
        // 停止之前的动画，避免冲突
        tween(cellNode).stop();
        
        // 选中动画：弹性放大效果
        tween(cellNode)
            .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
            .start();
    }
    
    /**
     * 取消选中的动画效果
     * @param cellNode 要取消选中的棋子节点
     */
    public static playUnselectAnimation(cellNode: Node): void {
        if (!cellNode) return;
        
        // 停止之前的动画
        tween(cellNode).stop();
        
        // 恢复动画：平滑缩回原始大小
        tween(cellNode)
            .to(0.15, { scale: new Vec3(1.0, 1.0, 1) })
            .start();
    }
    
    /**
     * 失败反馈动画 - 红色闪烁效果
     * @param cellNode 要播放失败动画的棋子节点
     * @param duration 动画持续时间
     */
    public static playFailureAnimation(cellNode: Node, duration: number = 1.0): void {
        if (!cellNode) return;
        
        // 停止之前的动画
        tween(cellNode).stop();
        
        // 失败动画：红色闪烁 + 轻微震动
        const originalScale = cellNode.scale.clone();
        
        tween(cellNode)
            .to(0.1, { scale: originalScale.multiplyScalar(1.15) })
            .to(0.1, { scale: originalScale.multiplyScalar(0.95) })
            .to(0.1, { scale: originalScale.multiplyScalar(1.1) })
            .to(0.1, { scale: originalScale.multiplyScalar(0.98) })
            .to(0.1, { scale: originalScale })
            .start();
    }
    
    /**
     * 消除动画 - 棋子消失效果
     * @param cellNode 要消除的棋子节点
     * @param callback 动画完成后的回调
     */
    public static playDestroyAnimation(cellNode: Node, callback?: () => void): void {
        if (!cellNode) {
            callback?.();
            return;
        }
        
        // 停止之前的动画
        tween(cellNode).stop();
        
        // 消除动画：缩放到0并淡出
        tween(cellNode)
            .to(0.2, { 
                scale: new Vec3(0, 0, 1),
            })
            .call(() => {
                cellNode.active = false;
                callback?.();
            })
            .start();
    }
    
    /**
     * 视觉震动效果
     * @param targetNode 要震动的节点
     * @param intensity 震动强度（像素）
     * @param duration 震动持续时间
     */
    public static playVisualShake(targetNode: Node, intensity: number = 5, duration: number = 0.3): void {
        if (!targetNode) return;
        
        const originalPosition = targetNode.position.clone();
        
        // 停止之前的震动动画
        tween(targetNode).stop();
        
        // 创建震动动画
        tween(targetNode)
            .to(0.05, { 
                position: originalPosition.add3f(intensity, 0, 0) 
            })
            .to(0.05, { 
                position: originalPosition.add3f(-intensity, 0, 0) 
            })
            .to(0.05, { 
                position: originalPosition.add3f(intensity, 0, 0) 
            })
            .to(0.05, { 
                position: originalPosition.add3f(-intensity, 0, 0) 
            })
            .to(0.1, { 
                position: originalPosition 
            })
            .start();
    }
    
    /**
     * 胜利庆祝动画
     * @param targetNode 要播放庆祝动画的节点
     */
    public static playVictoryAnimation(targetNode: Node): void {
        if (!targetNode) return;
        
        // 停止之前的动画
        tween(targetNode).stop();
        
        const originalScale = targetNode.scale.clone();
        
        // 胜利动画：弹跳效果
        tween(targetNode)
            .to(0.2, { scale: originalScale.multiplyScalar(1.3) })
            .to(0.2, { scale: originalScale.multiplyScalar(0.9) })
            .to(0.2, { scale: originalScale.multiplyScalar(1.1) })
            .to(0.2, { scale: originalScale })
            .start();
    }
    
    /**
     * 批量重置动画状态
     * @param cellNodes 要重置的棋子节点数组
     */
    public static resetAnimations(cellNodes: Node[]): void {
        cellNodes.forEach(cellNode => {
            if (cellNode && cellNode.active) {
                tween(cellNode).stop();
                tween(cellNode)
                    .to(0.15, { scale: new Vec3(1.0, 1.0, 1) })
                    .start();
            }
        });
    }
    
    /**
     * 停止节点的所有动画
     * @param cellNode 要停止动画的节点
     */
    public static stopAllAnimations(cellNode: Node): void {
        if (cellNode) {
            tween(cellNode).stop();
        }
    }
    
    /**
     * 入场动画 - 棋子从小到大出现
     * @param cellNode 要播放入场动画的棋子节点
     * @param delay 延迟时间
     */
    public static playEntranceAnimation(cellNode: Node, delay: number = 0): void {
        if (!cellNode) return;
        
        // 初始状态：缩放为0
        cellNode.scale = new Vec3(0, 0, 1);
        
        // 延迟后播放入场动画
        tween(cellNode)
            .delay(delay)
            .to(0.3, { scale: new Vec3(1.0, 1.0, 1) })
            .start();
    }
    
    /**
     * 连接路径动画（可选功能）
     * @param startNode 起始节点
     * @param endNode 结束节点
     * @param pathPoints 路径点数组
     */
    public static playConnectionAnimation(startNode: Node, endNode: Node, pathPoints?: Vec3[]): void {
        // 这里可以实现连接线的动画效果
        // 例如：从起始点到结束点画一条线，然后消失
    }
}
