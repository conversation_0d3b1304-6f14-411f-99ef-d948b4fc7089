<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>coin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,100}</string>
                <key>spriteSourceSize</key>
                <string>{100,100}</string>
                <key>textureRect</key>
                <string>{{1,601},{100,100}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>coinNum.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,2}</string>
                <key>spriteSize</key>
                <string>{190,52}</string>
                <key>spriteSourceSize</key>
                <string>{200,60}</string>
                <key>textureRect</key>
                <string>{{1,547},{190,52}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>heart.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,1}</string>
                <key>spriteSize</key>
                <string>{100,98}</string>
                <key>spriteSourceSize</key>
                <string>{100,100}</string>
                <key>textureRect</key>
                <string>{{103,601},{100,98}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>hint.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,180}</string>
                <key>spriteSourceSize</key>
                <string>{200,180}</string>
                <key>textureRect</key>
                <string>{{1,1},{200,180}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pause.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,180}</string>
                <key>spriteSourceSize</key>
                <string>{200,180}</string>
                <key>textureRect</key>
                <string>{{1,183},{200,180}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>shuffle.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,180}</string>
                <key>spriteSourceSize</key>
                <string>{200,180}</string>
                <key>textureRect</key>
                <string>{{1,365},{200,180}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>ui.png</string>
            <key>size</key>
            <string>{202,702}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:519bc55774c2d58bc4b9b5b2a3f464d2:32fbebf013c14ffee7442d9eb03b95c4:e489eb72a1dac3e9a97caaf8aad1ac42$</string>
            <key>textureFileName</key>
            <string>ui.png</string>
        </dict>
    </dict>
</plist>
