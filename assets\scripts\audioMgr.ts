import { _decorator, Component, Node, AudioSource, AudioClip, tween, CCFloat } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 音频播放状态枚举
 */
enum AudioState {
    NONE = "none",           // 无音频播放
    LOADING = "loading",     // 播放加载音乐
    GAMEPLAY = "gameplay"    // 播放游戏音乐
}

/**
 * 音频管理器 - 负责管理游戏中的所有音频播放
 * 包括加载音乐、游戏背景音乐和音效
 */
@ccclass('audioMgr')
export class audioMgr extends Component {

    // ==================== 音频资源属性 ====================

    @property({type: AudioClip, displayName: "加载音乐"})
    loadingMusic: AudioClip = null;

    @property({type: AudioClip, displayName: "游戏背景音乐"})
    gameplayBGM: AudioClip = null;

    @property({type: [AudioClip], displayName: "音效列表"})
    soundEffects: AudioClip[] = [];

    // ==================== 音频源组件 ====================

    @property({type: AudioSource, displayName: "背景音乐播放器"})
    bgmAudioSource: AudioSource = null;

    @property({type: AudioSource, displayName: "音效播放器"})
    sfxAudioSource: AudioSource = null;

    // ==================== 音频控制属性 ====================

    @property({type: CCFloat, range: [0, 1, 0.1], displayName: "背景音乐音量"})
    bgmVolume: number = 0.7;

    @property({type: CCFloat, range: [0, 1, 0.1], displayName: "音效音量"})
    sfxVolume: number = 0.8;

    @property({displayName: "启用音频"})
    audioEnabled: boolean = true;

    // ==================== 私有变量 ====================

    /** 当前播放状态 */
    private currentState: AudioState = AudioState.NONE;

    /** 当前背景音乐是否正在播放 */
    private isBGMPlaying: boolean = false;

    /** 音频淡入淡出的时间 */
    private readonly FADE_DURATION: number = 1.0;

    /** 单例实例 */
    private static instance: audioMgr = null;

    // ==================== 生命周期方法 ====================

    onLoad() {
        // 设置单例
        if (audioMgr.instance === null) {
            audioMgr.instance = this;
            // 保持音频管理器在场景切换时不被销毁
            if (this.node.parent) {
                this.node.parent = null;
            }
            // 标记为持久节点
            (this.node as any)._persistNode = true;
        } else {
            // 如果已经存在实例，销毁当前节点
            this.node.destroy();
            return;
        }

        this.initializeAudioSources();
    }

    start() {
        // 开始播放加载音乐
        this.playLoadingMusic();
    }

    onDestroy() {
        // 清理单例引用
        if (audioMgr.instance === this) {
            audioMgr.instance = null;
        }
    }

    // ==================== 静态方法 ====================

    /**
     * 获取音频管理器单例实例
     */
    public static getInstance(): audioMgr {
        return audioMgr.instance;
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化音频源组件
     */
    private initializeAudioSources() {
        // 如果没有设置音频源，自动创建
        if (!this.bgmAudioSource) {
            this.bgmAudioSource = this.createAudioSource("BGM_AudioSource");
        }

        if (!this.sfxAudioSource) {
            this.sfxAudioSource = this.createAudioSource("SFX_AudioSource");
        }

        // 设置音频源属性
        this.setupAudioSource(this.bgmAudioSource, this.bgmVolume, true);
        this.setupAudioSource(this.sfxAudioSource, this.sfxVolume, false);
    }

    /**
     * 创建音频源组件
     */
    private createAudioSource(name: string): AudioSource {
        const audioNode = new Node(name);
        audioNode.parent = this.node;
        return audioNode.addComponent(AudioSource);
    }

    /**
     * 设置音频源属性
     */
    private setupAudioSource(audioSource: AudioSource, volume: number, loop: boolean) {
        audioSource.volume = volume;
        audioSource.loop = loop;
        audioSource.playOnAwake = false;
    }

    // ==================== 公共音频控制方法 ====================

    /**
     * 播放加载音乐
     * 在游戏初始化或加载界面时调用
     */
    public playLoadingMusic() {
        if (!this.audioEnabled || !this.loadingMusic) {
            console.warn("音频已禁用或加载音乐未设置");
            return;
        }

        this.currentState = AudioState.LOADING;

        // 停止当前播放的音乐
        this.stopCurrentBGM();

        // 播放加载音乐
        this.bgmAudioSource.clip = this.loadingMusic;
        this.bgmAudioSource.play();
        this.isBGMPlaying = true;
    }

    /**
     * 播放游戏背景音乐
     * 在进入游戏时调用，会平滑过渡停止加载音乐
     */
    public playGameplayBGM() {
        if (!this.audioEnabled || !this.gameplayBGM) {
            console.warn("音频已禁用或游戏背景音乐未设置");
            return;
        }

        this.currentState = AudioState.GAMEPLAY;

        // 平滑过渡到游戏音乐
        this.transitionToGameplayBGM();
    }

    /**
     * 播放音效
     * @param effectName 音效名称或索引
     * @param volume 音量（可选，默认使用设置的音效音量）
     */
    public playSoundEffect(effectName: string | number, volume?: number) {
        if (!this.audioEnabled || !this.sfxAudioSource) {
            return;
        }

        let effectClip: AudioClip = null;

        // 根据参数类型获取音效
        if (typeof effectName === 'string') {
            effectClip = this.getSoundEffectByName(effectName);
        } else if (typeof effectName === 'number') {
            effectClip = this.soundEffects[effectName];
        }

        if (!effectClip) {
            console.warn(`未找到音效: ${effectName}`);
            return;
        }

        // 播放音效
        const playVolume = volume !== undefined ? volume : this.sfxVolume;
        this.sfxAudioSource.playOneShot(effectClip, playVolume);
    }

    /**
     * 停止当前背景音乐
     */
    public stopCurrentBGM() {
        if (this.bgmAudioSource && this.bgmAudioSource.playing) {
            this.bgmAudioSource.stop();
            this.isBGMPlaying = false;
        }
    }

    /**
     * 暂停背景音乐
     */
    public pauseBGM() {
        if (this.bgmAudioSource && this.bgmAudioSource.playing) {
            this.bgmAudioSource.pause();
        }
    }

    /**
     * 恢复背景音乐
     */
    public resumeBGM() {
        if (this.bgmAudioSource && !this.bgmAudioSource.playing) {
            this.bgmAudioSource.play();
        }
    }

    // ==================== 音量控制方法 ====================

    /**
     * 设置背景音乐音量
     * @param volume 音量值 (0-1)
     */
    public setBGMVolume(volume: number) {
        this.bgmVolume = Math.max(0, Math.min(1, volume));
        if (this.bgmAudioSource) {
            this.bgmAudioSource.volume = this.bgmVolume;
        }
    }

    /**
     * 设置音效音量
     * @param volume 音量值 (0-1)
     */
    public setSFXVolume(volume: number) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        if (this.sfxAudioSource) {
            this.sfxAudioSource.volume = this.sfxVolume;
        }
    }

    /**
     * 启用或禁用音频
     * @param enabled 是否启用音频
     */
    public setAudioEnabled(enabled: boolean) {
        this.audioEnabled = enabled;
        if (!enabled) {
            this.stopCurrentBGM();
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 平滑过渡到游戏背景音乐
     */
    private transitionToGameplayBGM() {
        if (this.bgmAudioSource.playing) {
            // 淡出当前音乐
            tween(this.bgmAudioSource)
                .to(this.FADE_DURATION, { volume: 0 })
                .call(() => {
                    // 切换到游戏音乐
                    this.bgmAudioSource.stop();
                    this.bgmAudioSource.clip = this.gameplayBGM;
                    this.bgmAudioSource.volume = this.bgmVolume;
                    this.bgmAudioSource.play();
                    this.isBGMPlaying = true;
                })
                .start();
        } else {
            // 直接播放游戏音乐
            this.bgmAudioSource.clip = this.gameplayBGM;
            this.bgmAudioSource.volume = this.bgmVolume;
            this.bgmAudioSource.play();
            this.isBGMPlaying = true;
        }
    }

    /**
     * 根据名称获取音效
     * @param name 音效名称
     */
    private getSoundEffectByName(name: string): AudioClip {
        return this.soundEffects.find(clip => clip && clip.name === name) || null;
    }

    // ==================== 游戏状态相关方法 ====================

    /**
     * 游戏开始时调用
     * 从加载音乐切换到游戏背景音乐
     */
    public onGameStart() {
        this.playGameplayBGM();
    }

    /**
     * 游戏暂停时调用
     */
    public onGamePause() {
        this.pauseBGM();
    }

    /**
     * 游戏恢复时调用
     */
    public onGameResume() {
        this.resumeBGM();
    }

    /**
     * 游戏结束时调用
     */
    public onGameEnd() {
        // 可以在这里播放结束音效或切换到其他音乐
    }

    // ==================== 便捷的音效播放方法 ====================

    /**
     * 播放点击音效
     */
    public playClickSound() {
        this.playSoundEffect("click");
    }

    /**
     * 播放成功消除音效
     */
    public playMatchSound() {
        this.playSoundEffect("match");
    }

    /**
     * 播放失败音效
     */
    public playFailSound() {
        this.playSoundEffect("fail");
    }

    /**
     * 播放胜利音效
     */
    public playWinSound() {
        this.playSoundEffect("win");
    }


}


