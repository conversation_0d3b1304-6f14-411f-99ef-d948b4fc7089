# 连连看游戏音频系统使用说明

## 概述

本音频系统为连连看游戏提供了完整的音频管理功能，包括：
- **加载音乐**：在游戏初始化和加载界面播放
- **游戏背景音乐**：在游戏进行时播放
- **音效系统**：提供点击、成功、失败等音效

## 功能特性

### 1. 音频状态管理
- `LOADING`：播放加载音乐状态
- `GAMEPLAY`：播放游戏背景音乐状态
- 平滑的音频过渡效果

### 2. 音频类型
- **背景音乐（BGM）**：循环播放的长音频
- **音效（SFX）**：短时间播放的音效

### 3. 音量控制
- 独立的BGM和SFX音量控制
- 音频启用/禁用功能

## 使用方法

### 1. 在场景中设置音频管理器

1. 在主场景中创建一个空节点，命名为 "AudioManager"
2. 为该节点添加 `audioMgr` 组件
3. 在组件属性中设置音频资源：

#### 必需的音频资源：
- **加载音乐**：拖入 `assets/audios/loading.mp3`
- **游戏背景音乐**：拖入 `assets/audios/mainBGM.mp3`

#### 可选的音效资源：
建议添加以下音效（需要自行准备音频文件）：
- `click.mp3` - 点击音效
- `match.mp3` - 成功消除音效  
- `fail.mp3` - 失败音效
- `win.mp3` - 胜利音效

### 2. 音频源设置

音频管理器会自动创建两个音频源：
- **BGM_AudioSource**：用于播放背景音乐
- **SFX_AudioSource**：用于播放音效

如果需要手动设置，可以：
1. 创建两个子节点并添加 `AudioSource` 组件
2. 在 `audioMgr` 组件中分别指定这两个音频源

### 3. 音量和设置调整

在 `audioMgr` 组件属性中可以调整：
- **背景音乐音量**：0-1 范围，默认 0.7
- **音效音量**：0-1 范围，默认 0.8  
- **启用音频**：总开关，默认启用

## 代码集成

### 在游戏管理器中的集成

音频管理器已经集成到 `gameMgr.ts` 中：

```typescript
// 获取音频管理器实例
this.audioManager = audioMgr.getInstance();

// 游戏开始时切换音乐
this.audioManager.onGameStart();

// 播放音效
this.audioManager.playClickSound();      // 点击音效
this.audioManager.playMatchSound();      // 成功音效  
this.audioManager.playFailSound();       // 失败音效
```

### 在其他脚本中使用

```typescript
import { audioMgr } from './audioMgr';

// 获取音频管理器实例
const audioManager = audioMgr.getInstance();

// 播放音效
audioManager.playSoundEffect("click");
audioManager.playSoundEffect(0); // 通过索引播放

// 控制背景音乐
audioManager.pauseBGM();
audioManager.resumeBGM();
audioManager.stopCurrentBGM();

// 音量控制
audioManager.setBGMVolume(0.5);
audioManager.setSFXVolume(0.8);
audioManager.setAudioEnabled(false);
```

## 音频播放流程

1. **游戏启动**：自动播放加载音乐
2. **加载完成**：2秒后自动切换到游戏背景音乐
3. **游戏交互**：
   - 点击棋子 → 播放点击音效
   - 成功消除 → 播放成功音效
   - 连接失败 → 播放失败音效

## 调试功能

音频管理器提供了调试方法：

```typescript
// 打印当前音频状态
audioManager.printAudioStatus();

// 获取音频信息
const info = audioManager.getAudioInfo();
console.log(info);
```

## 注意事项

1. **单例模式**：音频管理器使用单例模式，确保全局只有一个实例
2. **场景持久化**：音频管理器会在场景切换时保持存在
3. **Web平台限制**：在Web平台上，音频播放需要用户交互后才能开始
4. **音频格式**：建议使用 MP3 格式的音频文件
5. **文件大小**：背景音乐建议压缩到合适大小，音效文件应尽量小

## 测试功能

项目包含了一个音频测试脚本 `audioTest.ts`，可以用来测试音频系统的各种功能：

### 使用测试脚本：

1. 在场景中创建一个UI节点作为测试界面
2. 为该节点添加 `audioTest` 组件
3. 设置测试按钮容器和状态显示标签
4. 运行游戏后可以通过按钮测试各种音频功能

### 测试功能包括：
- 播放加载音乐/游戏音乐
- 暂停/恢复/停止背景音乐
- 播放各种音效
- 调整音量
- 切换音频开关
- 查看音频状态

## 扩展功能

可以根据需要扩展以下功能：
- 添加更多音效类型
- 实现音频淡入淡出效果
- 添加音频设置保存功能
- 实现动态音频加载

## 故障排除

### 常见问题：

1. **音频不播放**
   - 检查音频文件是否正确设置
   - 确认音频启用状态
   - 检查音量设置

2. **音频切换不平滑**
   - 确认音频文件格式正确
   - 检查音频文件是否损坏

3. **音效不播放**
   - 确认音效文件已添加到音效列表
   - 检查音效名称是否正确
   - 确认SFX音量设置

通过以上配置，您的连连看游戏将拥有完整的音频体验！
