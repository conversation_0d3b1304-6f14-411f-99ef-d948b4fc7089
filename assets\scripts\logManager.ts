import { LogConfig } from './config';

/**
 * 日志管理器
 * 统一管理所有模块的日志输出，支持模块级别的开关控制
 */
export class LogManager {
    
    /**
     * 游戏管理器日志 - 细化分类
     */
    static gameManager = {
        // 生命周期日志（启动、进入游戏等）
        lifecycle: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerLifecycle)) {
                    console.log(`🚀 [GameManager-Lifecycle] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerLifecycle)) {
                    console.warn(`🚀 [GameManager-Lifecycle] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🚀 [GameManager-Lifecycle] ${message}`, ...args);
                }
            }
        },

        // 系统初始化日志（音频、振动、Toast等）
        system: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerSystem)) {
                    console.log(`🔧 [GameManager-System] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerSystem)) {
                    console.warn(`🔧 [GameManager-System] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🔧 [GameManager-System] ${message}`, ...args);
                }
            }
        },

        // 棋盘相关日志（初始化、洗牌、创建等）
        grid: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerGrid)) {
                    console.log(`🎯 [GameManager-Grid] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerGrid)) {
                    console.warn(`🎯 [GameManager-Grid] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🎯 [GameManager-Grid] ${message}`, ...args);
                }
            }
        },

        // 棋子点击日志（频繁输出）
        click: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerClick)) {
                    console.log(`🖱️ [GameManager-Click] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerClick)) {
                    console.warn(`🖱️ [GameManager-Click] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🖱️ [GameManager-Click] ${message}`, ...args);
                }
            }
        },

        // 游戏胜利日志（重要事件）
        win: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerWin)) {
                    console.log(`🏆 [GameManager-Win] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerWin)) {
                    console.warn(`🏆 [GameManager-Win] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🏆 [GameManager-Win] ${message}`, ...args);
                }
            }
        },

        // 渐进式展现日志（输出较多）
        progressive: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerProgressive)) {
                    console.log(`🎬 [GameManager-Progressive] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerProgressive)) {
                    console.warn(`🎬 [GameManager-Progressive] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🎬 [GameManager-Progressive] ${message}`, ...args);
                }
            }
        },

        // 测试功能日志（仅开发时需要）
        test: {
            log: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerTest)) {
                    console.log(`🧪 [GameManager-Test] ${message}`, ...args);
                }
            },
            warn: (message: string, ...args: any[]) => {
                if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerTest)) {
                    console.warn(`🧪 [GameManager-Test] ${message}`, ...args);
                }
            },
            error: (message: string, ...args: any[]) => {
                if (LogConfig.enableErrorLogs) {
                    console.error(`🧪 [GameManager-Test] ${message}`, ...args);
                }
            }
        },

        // 保留原有的通用方法以保持向后兼容
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                console.log(`🎮 [GameManager] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                console.warn(`🎮 [GameManager] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎮 [GameManager] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 连接系统日志
     */
    static connectionSystem = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.connectionSystem) {
                console.log(`🔗 [Connection] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.connectionSystem) {
                console.warn(`🔗 [Connection] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔗 [Connection] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 选择逻辑日志
     */
    static selectionLogic = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.selectionLogic) {
                console.log(`👆 [Selection] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.selectionLogic) {
                console.warn(`👆 [Selection] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`👆 [Selection] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 动画状态日志
     */
    static animationState = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.animationState) {
                console.log(`🎬 [Animation] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.animationState) {
                console.warn(`🎬 [Animation] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎬 [Animation] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 无解检测器日志
     */
    static deadlockDetector = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.deadlockDetector) {
                console.log(`🔍 [Deadlock] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.deadlockDetector) {
                console.warn(`🔍 [Deadlock] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔍 [Deadlock] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 洗牌算法日志
     */
    static shuffleAlgorithm = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.shuffleAlgorithm) {
                console.log(`🔀 [Shuffle] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.shuffleAlgorithm) {
                console.warn(`🔀 [Shuffle] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔀 [Shuffle] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 菜单管理器日志
     */
    static menuManager = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.menuManager) {
                console.log(`🎛️ [Menu] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.menuManager) {
                console.warn(`🎛️ [Menu] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎛️ [Menu] ${message}`, ...args);
            }
        }
    };
    
    /**
     * Toast系统日志
     */
    static toastSystem = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.toastSystem) {
                console.log(`🍞 [Toast] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.toastSystem) {
                console.warn(`🍞 [Toast] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🍞 [Toast] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 音频管理器日志
     */
    static audioManager = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.audioManager) {
                console.log(`🔊 [Audio] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.audioManager) {
                console.warn(`🔊 [Audio] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔊 [Audio] ${message}`, ...args);
            }
        }
    };
    

    
    /**
     * 加载界面日志
     */
    static loading = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.loading) {
                console.log(`🎮 [Loading] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.loading) {
                console.warn(`🎮 [Loading] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎮 [Loading] ${message}`, ...args);
            }
        }
    };

    /**
     * 棋子组件日志
     */
    static cellComponent = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.cellComponent) {
                console.log(`🎯 [Cell] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.cellComponent) {
                console.warn(`🎯 [Cell] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎯 [Cell] ${message}`, ...args);
            }
        }
    };



    /**
     * 振动系统日志
     */
    static vibration = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.vibration) {
                console.log(`📳 [Vibration] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.vibration) {
                console.warn(`📳 [Vibration] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`📳 [Vibration] ${message}`, ...args);
            }
        }
    };

    /**
     * 通用日志方法（用于临时调试）
     */
    static temp = {
        log: (message: string, ...args: any[]) => {
            console.log(`🔧 [Temp] ${message}`, ...args);
        },
        warn: (message: string, ...args: any[]) => {
            console.warn(`🔧 [Temp] ${message}`, ...args);
        },
        error: (message: string, ...args: any[]) => {
            console.error(`🔧 [Temp] ${message}`, ...args);
        }
    };
    
    /**
     * 批量设置日志开关
     */
    static setLogLevel(level: 'all' | 'errors' | 'none') {
        switch (level) {
            case 'all':
                LogConfig.enableAllLogs = true;
                console.log('🔧 [LogManager] 已启用所有日志');
                break;
            case 'errors':
                LogConfig.enableAllLogs = false;
                LogConfig.enableErrorLogs = true;
                console.log('🔧 [LogManager] 只显示错误日志');
                break;
            case 'none':
                LogConfig.enableAllLogs = false;
                LogConfig.enableErrorLogs = false;
                console.log('🔧 [LogManager] 已关闭所有日志');
                break;
        }
    }
}
