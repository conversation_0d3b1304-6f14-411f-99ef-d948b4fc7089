import { Node } from 'cc';
import { cellFixed } from './cellFixed';
import { AnimationStateManager, AnimationState } from './animationStateManager';
import { ConnectionSystem } from './connectionSystem';
import { VibrationSystem } from './vibrationSystem';
import { AudioManager } from './audioManager';
import { LogManager } from './logManager';
import { ConnectionPathRenderer } from './connectionPathRenderer';

/**
 * 选择逻辑管理器 - 处理棋子选择的完整逻辑流程
 * 确保动画状态正确且无冲突
 */
export class SelectionLogicManager {
    
    private selectedCell: Node = null;
    private animationManager: AnimationStateManager;
    private connectionSystem: ConnectionSystem;
    private vibrationSystem: VibrationSystem;
    private audioManager: AudioManager;
    private gridNode: Node;
    private pathRenderer: ConnectionPathRenderer;
    
    constructor(connectionSystem: ConnectionSystem, gridNode: Node) {
        this.animationManager = AnimationStateManager.getInstance();
        this.connectionSystem = connectionSystem;
        this.vibrationSystem = VibrationSystem.getInstance();
        this.audioManager = AudioManager.getInstance();
        this.gridNode = gridNode;

        // 初始化路径渲染器
        this.initializePathRenderer();
    }

    /**
     * 初始化路径渲染器
     */
    private initializePathRenderer(): void {
        if (!this.gridNode) {
            return;
        }

        // 在网格节点上添加路径渲染器组件
        this.pathRenderer = this.gridNode.getComponent(ConnectionPathRenderer);
        if (!this.pathRenderer) {
            this.pathRenderer = this.gridNode.addComponent(ConnectionPathRenderer);
        }
    }
    
    /**
     * 处理棋子点击的完整逻辑
     * @param cellNode 被点击的棋子节点
     * @param onDestroy 消除完成的回调
     */
    public handleCellClick(cellNode: Node, onDestroy?: (cell1: Node, cell2: Node) => void): void {
        LogManager.selectionLogic.log(`🖱️ 点击棋子: ${cellNode.name}`);

        // 首先清理所有异常状态（失败动画等）
        this.animationManager.clearAbnormalStates();

        // 清理所有提示状态（用户点击后清除提示）
        this.animationManager.clearHintStates();

        // 强力清理所有异常缩放状态（确保修复bug）
        this.animationManager.forceCleanAllAbnormalScales(this.gridNode);

        // 播放点击音效
        if (this.audioManager) {
            this.audioManager.playClickSound();
        }

        // 情况1：首次选择
        if (!this.selectedCell) {
            this.selectFirstCell(cellNode);
            return;
        }

        // 情况2：点击同一棋子，取消选择
        if (this.selectedCell === cellNode) {
            this.unselectCell();
            return;
        }

        // 情况3：选择第二个棋子
        this.handleSecondSelection(cellNode, onDestroy);
    }
    
    /**
     * 选择第一个棋子
     */
    private selectFirstCell(cellNode: Node): void {
        this.selectedCell = cellNode;
        this.animationManager.setSelected(cellNode);
        
        const cellType = cellNode.getComponent(cellFixed)?.getType();
        LogManager.selectionLogic.log(`🎯 选中第一个棋子，类型: ${cellType}`);
    }
    
    /**
     * 取消选中棋子
     */
    private unselectCell(): void {
        if (this.selectedCell) {
            this.animationManager.setNormal(this.selectedCell);
            this.selectedCell = null;
            LogManager.selectionLogic.log("❌ 取消选中");
        }
    }
    
    /**
     * 处理第二个棋子的选择
     */
    private handleSecondSelection(cellNode: Node, onDestroy?: (cell1: Node, cell2: Node) => void): void {
        const firstCell = this.selectedCell;
        const firstType = firstCell.getComponent(cellFixed)?.getType();
        const secondType = cellNode.getComponent(cellFixed)?.getType();
        
        LogManager.selectionLogic.log(`🔍 比较棋子类型: ${firstType} vs ${secondType}`);
        
        // 检查类型是否匹配
        if (firstType !== secondType) {
            // 类型不匹配，切换选择到新棋子
            this.switchSelection(firstCell, cellNode);
            return;
        }
        
        // 类型匹配，检查连接
        this.handleMatchingPair(firstCell, cellNode, onDestroy);
    }
    
    /**
     * 切换选择到新棋子（类型不匹配时）
     */
    private switchSelection(oldCell: Node, newCell: Node): void {
        LogManager.selectionLogic.log("🔄 类型不匹配，切换选择");
        
        // 先将旧棋子恢复正常状态
        this.animationManager.setNormal(oldCell);
        
        // 然后选中新棋子
        this.selectedCell = newCell;
        this.animationManager.setSelected(newCell);
        
        const newType = newCell.getComponent(cellFixed)?.getType();
        LogManager.selectionLogic.log(`🎯 切换到新棋子，类型: ${newType}`);
    }
    
    /**
     * 处理匹配的棋子对
     */
    private handleMatchingPair(firstCell: Node, secondCell: Node, onDestroy?: (cell1: Node, cell2: Node) => void): void {
        // 先将第二个棋子也设为选中状态，显示两个都被选中
        this.animationManager.setSelected(secondCell);
        
        // 检查是否可以连接
        if (this.connectionSystem.canConnect(firstCell, secondCell)) {
            LogManager.selectionLogic.log("🎉 连接成功，准备消除");
            this.handleSuccessfulConnection(firstCell, secondCell, onDestroy);
        } else {
            LogManager.selectionLogic.log("⚠️ 无法连接，显示失败反馈");
            this.handleFailedConnection(firstCell, secondCell);
        }
    }
    
    /**
     * 处理成功连接
     */
    private handleSuccessfulConnection(firstCell: Node, secondCell: Node, onDestroy?: (cell1: Node, cell2: Node) => void): void {
        // 绘制连接路径
        this.drawConnectionPath(firstCell, secondCell);

        // 播放成功音效
        if (this.audioManager) {
            this.audioManager.playMatchSound();
        }

        // 清除选中状态
        this.selectedCell = null;

        // 播放消除动画
        let completedCount = 0;
        const onAnimationComplete = () => {
            completedCount++;
            if (completedCount === 2) {
                // 延迟清理路径，让路径动画完成
                setTimeout(() => {
                    if (this.pathRenderer) {
                        this.pathRenderer.clearPath();
                    }
                }, 1000); // 1秒后清理

                onDestroy?.(firstCell, secondCell);
            }
        };

        this.animationManager.playDestroyAnimation(firstCell, onAnimationComplete);
        this.animationManager.playDestroyAnimation(secondCell, onAnimationComplete);

        LogManager.selectionLogic.log("✅ 消除动画已开始");
    }

    /**
     * 绘制连接路径
     */
    private drawConnectionPath(cell1: Node, cell2: Node): void {
        if (!this.pathRenderer || !this.connectionSystem) {
            return;
        }

        // 获取连接路径
        const pathPoints = this.connectionSystem.getConnectionPath(cell1, cell2);

        if (pathPoints && pathPoints.length >= 2) {
            // 绘制连接路径
            this.pathRenderer.drawConnectionPath(cell1, cell2, pathPoints);
        }
    }
    
    /**
     * 处理失败连接
     */
    private handleFailedConnection(firstCell: Node, secondCell: Node): void {
        // 播放震动反馈
        this.vibrationSystem.vibrateFailure(firstCell);

        // 播放失败动画，并在动画完成后自动重置
        this.animationManager.playFailureAnimation(firstCell, () => {
            this.animationManager.setNormal(firstCell);
        });

        this.animationManager.playFailureAnimation(secondCell, () => {
            this.animationManager.setNormal(secondCell);
        });

        // 清除选中状态
        this.selectedCell = null;

        LogManager.selectionLogic.log("💥 失败反馈已触发");
    }
    

    
    /**
     * 强制清理所有状态
     */
    public forceCleanAllStates(): void {
        // 清除选中状态
        if (this.selectedCell) {
            this.animationManager.setNormal(this.selectedCell);
            this.selectedCell = null;
        }
        
        // 清理动画管理器中的所有状态
        this.animationManager.clearAllStates();
        
        LogManager.selectionLogic.log("🧹 强制清理所有选择状态");
    }
    
    /**
     * 获取当前选中的棋子
     */
    public getSelectedCell(): Node {
        return this.selectedCell;
    }
    
    /**
     * 检查是否有选中的棋子
     */
    public hasSelectedCell(): boolean {
        return this.selectedCell !== null;
    }
    
    /**
     * 检查指定棋子是否被选中
     */
    public isSelected(cellNode: Node): boolean {
        return this.selectedCell === cellNode;
    }
    
    /**
     * 显示提示：高亮一对可连接的棋子
     * @returns 是否找到并显示了提示
     */
    public showHint(): boolean {
        LogManager.selectionLogic.log("💡 请求显示提示");

        // 先清理之前的提示状态
        this.animationManager.clearHintStates();

        // 查找可连接的棋子对
        const connectablePair = this.connectionSystem.findConnectablePair();

        if (connectablePair) {
            const { cell1, cell2 } = connectablePair;

            // 播放提示动画
            this.animationManager.playHintAnimation(cell1);
            this.animationManager.playHintAnimation(cell2);

            LogManager.selectionLogic.log(`💡 显示提示: ${cell1.name} 和 ${cell2.name} 可以连接`);
            return true;
        } else {
            LogManager.selectionLogic.log("💡 没有找到可连接的棋子对");
            return false;
        }
    }

    /**
     * 检查是否还有可连接的棋子对
     * @returns 是否存在可连接的棋子对
     */
    public hasConnectablePairs(): boolean {
        return this.connectionSystem.hasConnectablePairs();
    }

    /**
     * 获取所有可连接的棋子对数量
     * @returns 可连接的棋子对数量
     */
    public getConnectablePairsCount(): number {
        return this.connectionSystem.getAllConnectablePairs().length;
    }

    /**
     * 获取调试信息
     */
    public getDebugInfo(): string {
        const selectedType = this.selectedCell ?
            this.selectedCell.getComponent(cellFixed)?.getType() :
            "无";

        const connectablePairs = this.getConnectablePairsCount();

        return `选择状态: ${this.hasSelectedCell() ? '有选中' : '无选中'}, 类型: ${selectedType}, 可连接对数: ${connectablePairs}`;
    }
}
