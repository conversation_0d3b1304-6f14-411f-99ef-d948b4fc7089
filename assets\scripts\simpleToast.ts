import { _decorator, Component, Node, Label, tween, Vec3, Color, UIOpacity, Sprite, UITransform } from 'cc';
const { ccclass, property } = _decorator;
import { LogManager } from './logManager';

/**
 * 简化版Toast提示系统
 * 直接在游戏管理器中使用，无需复杂的单例模式
 */
@ccclass('simpleToast')
export class simpleToast extends Component {
    
    @property({ type: Node, displayName: "Toast容器", tooltip: "Toast显示的父节点，如果不设置则使用当前节点" })
    toastContainer: Node = null!;

    // Toast队列管理
    private activeToasts: Node[] = [];
    private readonly MAX_TOASTS = 2;        // 最大同时显示的Toast数量（减少到2个）
    private readonly TOAST_SPACING = 90;   // Toast之间的间距
    private readonly BASE_Y_POSITION = 250; // 基础Y位置（更靠上）

    // Toast样式配置 - 专注于无解和打乱提示
    private readonly COLORS = {
        info: new Color(255, 255, 255, 255),     // 白色文字
        success: new Color(255, 255, 255, 255),  // 白色文字
        warning: new Color(255, 255, 255, 255),  // 白色文字
        error: new Color(255, 255, 255, 255)     // 白色文字
    };

    private readonly BACKGROUND_COLORS = {
        info: new Color(52, 152, 219, 200),      // 蓝色背景
        success: new Color(46, 204, 113, 200),   // 绿色背景
        warning: new Color(241, 196, 15, 200),   // 橙色背景
        error: new Color(231, 76, 60, 200)       // 红色背景
    };

    private readonly ICONS = {
        info: "💡",      // 提示灯泡 - 用于提示信息
        success: "🎯",   // 靶心 - 用于成功完成，更符合游戏主题
        warning: "�",   // 交叉箭头 - 用于重新排列，更直观
        error: "⚠️"      // 警告标志 - 用于错误警告
    };
    
    onLoad() {
        // 如果没有设置容器，使用当前节点
        if (!this.toastContainer) {
            this.toastContainer = this.node;
        }
        
        LogManager.toastSystem.log("简化Toast系统初始化完成");
    }
    
    /**
     * 显示信息提示
     */
    public showInfo(message: string, duration: number = 2000): void {
        this.showToast(message, 'info', duration);
    }
    
    /**
     * 显示成功提示
     */
    public showSuccess(message: string, duration: number = 2000): void {
        this.showToast(message, 'success', duration);
    }
    
    /**
     * 显示警告提示
     */
    public showWarning(message: string, duration: number = 2000): void {
        this.showToast(message, 'warning', duration);
    }
    
    /**
     * 显示错误提示
     */
    public showError(message: string, duration: number = 2000): void {
        this.showToast(message, 'error', duration);
    }
    
    /**
     * 显示Toast提示
     */
    private showToast(message: string, type: 'info' | 'success' | 'warning' | 'error', duration: number): void {
        // 检查是否有相同的Toast正在显示，避免重复
        if (this.isDuplicateToast(message)) {
            LogManager.toastSystem.log(`跳过重复Toast: ${message}`);
            return;
        }

        // 如果Toast数量超过限制，移除最旧的
        this.manageToastQueue();

        // 创建Toast节点
        const toastNode = this.createToastNode(message, type);
        if (!toastNode) {
            LogManager.toastSystem.error("创建Toast节点失败");
            return;
        }

        // 添加到容器和队列
        toastNode.parent = this.toastContainer;
        toastNode.active = true;
        this.activeToasts.push(toastNode);

        // 设置位置（避免重叠）
        this.setToastPosition(toastNode);

        // 设置初始状态
        toastNode.scale = new Vec3(0.8, 0.8, 1);
        const opacity = toastNode.getComponent(UIOpacity);
        if (opacity) {
            opacity.opacity = 0;
        }

        // 播放显示动画
        this.playShowAnimation(toastNode, () => {
            // 延迟后播放隐藏动画
            setTimeout(() => {
                this.playHideAnimation(toastNode, () => {
                    // 从队列中移除并销毁节点
                    this.removeToastFromQueue(toastNode);
                    if (toastNode && toastNode.isValid) {
                        toastNode.destroy();
                    }
                    // 重新排列剩余的Toast位置
                    this.rearrangeToasts();
                });
            }, duration);
        });

        LogManager.toastSystem.log(`显示Toast: ${message} (${type})`);
    }
    
    /**
     * 检查是否有重复的Toast
     */
    private isDuplicateToast(message: string): boolean {
        return this.activeToasts.some(toast => {
            return toast && toast.isValid && toast['_toastMessage'] === message;
        });
    }

    /**
     * 管理Toast队列，移除超出限制的Toast
     */
    private manageToastQueue(): void {
        while (this.activeToasts.length >= this.MAX_TOASTS) {
            const oldestToast = this.activeToasts.shift();
            if (oldestToast && oldestToast.isValid) {
                // 立即隐藏最旧的Toast
                this.playHideAnimation(oldestToast, () => {
                    if (oldestToast && oldestToast.isValid) {
                        oldestToast.destroy();
                    }
                });
            }
        }
    }

    /**
     * 设置Toast位置，避免重叠
     */
    private setToastPosition(toastNode: Node): void {
        const index = this.activeToasts.length - 1;
        const yPosition = this.BASE_Y_POSITION - (index * this.TOAST_SPACING);
        toastNode.setPosition(0, yPosition, 0);
    }

    /**
     * 从队列中移除Toast
     */
    private removeToastFromQueue(toastNode: Node): void {
        const index = this.activeToasts.indexOf(toastNode);
        if (index > -1) {
            this.activeToasts.splice(index, 1);
        }
    }

    /**
     * 重新排列剩余Toast的位置
     */
    private rearrangeToasts(): void {
        this.activeToasts.forEach((toast, index) => {
            if (toast && toast.isValid) {
                const targetY = this.BASE_Y_POSITION - (index * this.TOAST_SPACING);
                // 平滑移动到新位置
                tween(toast)
                    .to(0.3, { position: new Vec3(0, targetY, 0) })
                    .start();
            }
        });
    }

    /**
     * 创建Toast节点
     */
    private createToastNode(message: string, type: 'info' | 'success' | 'warning' | 'error'): Node | null {
        const toastNode = new Node('Toast');

        // 创建背景节点
        const backgroundNode = this.createToastBackground(type);
        backgroundNode.parent = toastNode;

        // 创建图标节点
        const iconNode = this.createIconNode(type);
        iconNode.parent = toastNode;
        iconNode.setPosition(-120, 0, 0); // 图标位置在左侧

        // 添加文字Label组件
        const label = toastNode.addComponent(Label);
        label.string = message;  // 不包含图标，图标单独显示
        label.color = this.COLORS[type];
        label.fontSize = 28;           // 稍微调小字体给图标留空间
        label.lineHeight = 32;
        label.isBold = true;           // 加粗字体

        // 设置文本对齐，稍微向右偏移给图标留空间
        label.horizontalAlign = Label.HorizontalAlign.CENTER;
        label.verticalAlign = Label.VerticalAlign.CENTER;

        // 添加透明度组件用于动画
        toastNode.addComponent(UIOpacity);

        // 存储消息内容用于重复检查
        toastNode['_toastMessage'] = message;

        return toastNode;
    }

    /**
     * 创建图标节点
     */
    private createIconNode(type: 'info' | 'success' | 'warning' | 'error'): Node {
        const iconNode = new Node('ToastIcon');

        // 根据类型创建不同的图标
        let iconText = "";
        switch (type) {
            case 'info':
                iconText = "💡";  // 灯泡
                break;
            case 'success':
                iconText = "🎯";  // 靶心
                break;
            case 'warning':
                iconText = "🔄";  // 循环箭头
                break;
            case 'error':
                iconText = "⚠️";   // 警告
                break;
        }

        // 添加图标Label
        const iconLabel = iconNode.addComponent(Label);
        iconLabel.string = iconText;
        iconLabel.color = this.COLORS[type];
        iconLabel.fontSize = 36;  // 图标字体更大
        iconLabel.isBold = true;

        // 设置图标对齐
        iconLabel.horizontalAlign = Label.HorizontalAlign.CENTER;
        iconLabel.verticalAlign = Label.VerticalAlign.CENTER;

        return iconNode;
    }

    /**
     * 创建Toast背景
     */
    private createToastBackground(type: 'info' | 'success' | 'warning' | 'error'): Node {
        const backgroundNode = new Node('ToastBackground');

        // 添加UITransform组件设置大小 - 增大背景尺寸
        const uiTransform = backgroundNode.addComponent(UITransform);
        uiTransform.setContentSize(400, 70);  // 增大背景尺寸

        // 添加Sprite组件作为背景
        const sprite = backgroundNode.addComponent(Sprite);
        sprite.color = this.BACKGROUND_COLORS[type];

        // 设置背景在文字后面
        backgroundNode.setSiblingIndex(0);

        return backgroundNode;
    }
    
    /**
     * 播放显示动画
     */
    private playShowAnimation(toastNode: Node, callback?: () => void): void {
        const opacity = toastNode.getComponent(UIOpacity);
        
        // 缩放动画
        tween(toastNode)
            .to(0.3, { scale: new Vec3(1, 1, 1) }, { easing: 'backOut' })
            .call(() => {
                callback?.();
            })
            .start();
        
        // 透明度动画
        if (opacity) {
            tween(opacity)
                .to(0.3, { opacity: 255 })
                .start();
        }
    }
    
    /**
     * 播放隐藏动画
     */
    private playHideAnimation(toastNode: Node, callback?: () => void): void {
        const opacity = toastNode.getComponent(UIOpacity);
        
        // 缩放动画
        tween(toastNode)
            .to(0.3, { scale: new Vec3(0.8, 0.8, 1) }, { easing: 'backIn' })
            .call(() => {
                callback?.();
            })
            .start();
        
        // 透明度动画
        if (opacity) {
            tween(opacity)
                .to(0.3, { opacity: 0 })
                .start();
        }
    }
    
    /**
     * 清除所有Toast
     */
    public clearAllToasts(): void {
        // 清除队列中的所有Toast
        this.activeToasts.forEach(toast => {
            if (toast && toast.isValid) {
                toast.destroy();
            }
        });
        this.activeToasts = [];

        // 清除容器中可能遗留的Toast节点
        if (this.toastContainer && this.toastContainer.children) {
            this.toastContainer.children.forEach(child => {
                if (child.name === 'Toast') {
                    child.destroy();
                }
            });
        }
    }
    
    /**
     * 显示无解提示 - 游戏专用
     */
    public showNoConnectionWarning(): void {
        this.showWarning("无可连接棋子，即将自动重排", 2500);
    }

    /**
     * 显示打乱完成提示 - 游戏专用
     */
    public showShuffleComplete(): void {
        this.showSuccess("重新排列完成！", 2000);
    }
}
