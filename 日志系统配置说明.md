# 日志系统配置说明

## 🎯 功能概述

为了解决控制台日志输出过多的问题，我已经实现了一个模块化的日志管理系统，可以精确控制每个模块的日志输出。

## 🔧 系统架构

### 1. **配置文件 (config.ts)**
```typescript
export const LogConfig = {
    // 游戏核心模块
    gameManager: true,          // 游戏管理器日志
    connectionSystem: false,    // 连接系统日志（输出较多，建议关闭）
    selectionLogic: false,      // 选择逻辑日志
    animationState: false,      // 动画状态日志
    
    // 检测和算法模块
    deadlockDetector: true,     // 无解检测日志
    shuffleAlgorithm: false,    // 洗牌算法详细日志
    
    // UI和交互模块
    menuManager: true,          // 菜单管理器日志
    toastSystem: true,          // Toast提示系统日志
    
    // 音频模块
    audioManager: false,        // 音频管理器日志
    
    // 调试模块
    debugInfo: false,           // 调试信息输出
    performance: false,         // 性能监控日志
    
    // 全局开关
    enableAllLogs: false,       // 设置为 true 时启用所有日志
    enableErrorLogs: true,      // 错误日志始终启用
};
```

### 2. **日志管理器 (logManager.ts)**
提供统一的日志接口，每个模块都有独立的日志控制：

```typescript
// 使用示例
LogManager.gameManager.log("游戏开始");
LogManager.connectionSystem.log("检测连接");
LogManager.deadlockDetector.warn("无解状态");
LogManager.shuffleAlgorithm.error("洗牌失败");
```

## 📊 模块日志分类

### 🎮 游戏核心模块
| 模块 | 图标 | 默认状态 | 说明 |
|------|------|----------|------|
| gameManager | 🎮 | ✅ 启用 | 游戏管理器核心日志 |
| connectionSystem | 🔗 | ❌ 关闭 | 连接检测（输出量大） |
| selectionLogic | 👆 | ❌ 关闭 | 选择逻辑（输出量大） |
| animationState | 🎬 | ❌ 关闭 | 动画状态（输出量大） |

### 🔍 检测和算法模块
| 模块 | 图标 | 默认状态 | 说明 |
|------|------|----------|------|
| deadlockDetector | 🔍 | ✅ 启用 | 无解检测重要信息 |
| shuffleAlgorithm | 🔀 | ❌ 关闭 | 洗牌算法详细过程 |

### 🎛️ UI和交互模块
| 模块 | 图标 | 默认状态 | 说明 |
|------|------|----------|------|
| menuManager | 🎛️ | ✅ 启用 | 菜单操作日志 |
| toastSystem | 🍞 | ✅ 启用 | Toast提示系统 |

### 🔊 其他模块
| 模块 | 图标 | 默认状态 | 说明 |
|------|------|----------|------|
| audioManager | 🔊 | ❌ 关闭 | 音频管理器 |
| debug | 🐛 | ❌ 关闭 | 调试信息 |
| performance | ⚡ | ❌ 关闭 | 性能监控 |

## 🛠️ 使用方法

### 1. **基本使用**
```typescript
// 在需要日志的文件中导入
import { LogManager } from './logManager';

// 使用对应模块的日志
LogManager.gameManager.log("游戏初始化完成");
LogManager.deadlockDetector.warn("检测到无解状态");
LogManager.shuffleAlgorithm.error("洗牌算法失败");
```

### 2. **日志级别**
每个模块都支持三个级别：
```typescript
LogManager.moduleName.log("普通信息");    // 普通日志
LogManager.moduleName.warn("警告信息");   // 警告日志
LogManager.moduleName.error("错误信息");  // 错误日志
```

### 3. **全局控制**
```typescript
// 启用所有日志（调试时使用）
LogManager.setLogLevel('all');

// 只显示错误日志（生产环境）
LogManager.setLogLevel('errors');

// 关闭所有日志
LogManager.setLogLevel('none');
```

## ⚙️ 配置建议

### 开发阶段配置：
```typescript
export const LogConfig = {
    gameManager: true,          // 保持核心日志
    deadlockDetector: true,     // 保持重要功能日志
    menuManager: true,          // 保持UI交互日志
    toastSystem: true,          // 保持用户反馈日志
    
    // 关闭高频输出的模块
    connectionSystem: false,    // 连接检测频繁
    selectionLogic: false,      // 选择逻辑频繁
    animationState: false,      // 动画状态频繁
    shuffleAlgorithm: false,    // 洗牌详细过程
    
    enableErrorLogs: true,      // 始终保持错误日志
};
```

### 调试特定功能时：
```typescript
// 调试连接系统时
LogConfig.connectionSystem = true;

// 调试洗牌算法时
LogConfig.shuffleAlgorithm = true;

// 调试动画问题时
LogConfig.animationState = true;
```

### 生产环境配置：
```typescript
export const LogConfig = {
    // 关闭所有普通日志
    gameManager: false,
    connectionSystem: false,
    selectionLogic: false,
    // ... 其他模块都设为 false
    
    // 只保留错误日志
    enableErrorLogs: true,
    enableAllLogs: false,
};
```

## 🔍 日志输出示例

### 启用状态下的输出：
```
🎮 [GameManager] 游戏初始化完成
🔍 [Deadlock] 开始检测无解状态...
🔍 [Deadlock] ✅ 检测完成：找到 3 对可连接的棋子
🎛️ [Menu] 点击提示按钮
🍞 [Toast] 简化Toast系统初始化完成
```

### 关闭状态下的输出：
```
(只显示错误日志，或完全静默)
```

## 📁 已更新的文件

### 1. **config.ts**
- ✅ 添加了 `LogConfig` 配置对象
- ✅ 定义了所有模块的日志开关

### 2. **logManager.ts** (新文件)
- ✅ 实现了模块化日志管理器
- ✅ 提供了统一的日志接口
- ✅ 支持全局日志级别控制

### 3. **gameMgrSimplified.ts**
- ✅ 导入了 LogManager
- ✅ 替换了洗牌算法的日志输出

### 4. **deadlockDetector.ts**
- ✅ 导入了 LogManager
- ✅ 替换了无解检测的日志输出

### 5. **simpleToast.ts**
- ✅ 导入了 LogManager
- ✅ 替换了Toast系统的日志输出

## 🎯 效果对比

### 修复前：
```
🔗 [Connection] 检测连接: cell_0_0 -> cell_0_1
🔗 [Connection] 检测连接: cell_0_0 -> cell_0_2
🔗 [Connection] 检测连接: cell_0_0 -> cell_0_3
... (大量连接检测日志)
👆 [Selection] 选择棋子: cell_2_3
👆 [Selection] 检查连接性
🎬 [Animation] 开始选中动画
🎬 [Animation] 动画完成
🔍 [Shuffle] 洗牌前棋子数量: 16
🔍 [Shuffle] 洗牌前类型分布: {...}
... (大量详细日志)
```

### 修复后（默认配置）：
```
🎮 [GameManager] 游戏初始化完成
🔍 [Deadlock] 检测到无解状态
🍞 [Toast] 显示Toast: 无可连接棋子，即将自动重排
🔍 [Deadlock] 开始打乱棋盘
🍞 [Toast] 显示Toast: 重新排列完成！
```

## 🚀 扩展功能

### 1. **运行时控制**
可以在游戏运行时动态调整日志：
```typescript
// 在控制台中执行
LogManager.setLogLevel('all');  // 临时启用所有日志
LogConfig.shuffleAlgorithm = true;  // 临时启用洗牌日志
```

### 2. **性能监控**
```typescript
LogManager.performance.log(`帧率: ${fps}, 内存: ${memory}MB`);
```

### 3. **用户反馈**
```typescript
LogManager.debug.log("用户操作:", { action: "click", target: "hint_button" });
```

通过这个日志系统，您现在可以精确控制每个模块的日志输出，大大减少控制台的噪音，同时保留重要的调试信息！
