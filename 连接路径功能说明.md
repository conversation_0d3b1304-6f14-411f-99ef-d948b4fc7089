# 连接路径功能说明

## 🎯 功能概述

连连看游戏的连接路径显示功能，当两个棋子成功连接消除时，会显示一条带圆角转折的连接路径线条，颜色与棋子背景色一致，并自动淡出消失。

## ✨ 功能特性

### 🎨 **视觉效果**
- **动态颜色匹配**：路径颜色自动匹配棋子背景色（稍微深一点）
- **圆角转折**：所有转折点使用平滑的圆角，视觉效果优雅
- **平滑动画**：路径出现时有轻微的缩放效果，然后平滑淡出

### 🔄 **支持的路径类型**
- **直线连接**：两个棋子直接相连
- **一转弯连接**：通过一个转折点连接
- **两转弯连接**：通过两个转折点连接
- **边界外连接**：通过游戏区域边界外的路径连接

### ⚙️ **可配置参数**
```typescript
@property({ type: CCFloat, displayName: "线条宽度" })
lineWidth: number = 4;

@property({ type: CCFloat, displayName: "动画持续时间" })
animationDuration: number = 0.8;

@property({ type: CCFloat, displayName: "线条透明度" })
lineOpacity: number = 200;

@property({ type: CCFloat, displayName: "圆角半径" })
cornerRadius: number = 15;
```

## 🔧 技术实现

### 核心组件

#### 1. **ConnectionPathRenderer**
- **文件**: `assets/scripts/connectionPathRenderer.ts`
- **功能**: 负责路径的绘制、动画和清理
- **特性**: 
  - 使用Graphics组件绘制路径
  - 支持圆角转折绘制
  - 自动颜色匹配和动画管理

#### 2. **ConnectionSystem 扩展**
- **新增方法**: `getConnectionPath(cell1, cell2)`
- **功能**: 获取两个棋子之间的具体连接路径坐标点
- **返回**: Vec3数组，包含路径的所有关键点

#### 3. **SelectionLogicManager 集成**
- **集成点**: 在成功连接处理中自动调用路径绘制
- **时机**: 在播放音效之后，消除动画之前

### 实现细节

#### 坐标转换
```typescript
// 将世界坐标转换为路径节点的本地坐标
const localPoints = pathPoints.map(worldPos => {
    const gridTransform = this.pathNode.parent?.getComponent(UITransform);
    return gridTransform.convertToNodeSpaceAR(worldPos);
});
```

#### 圆角绘制
```typescript
// 使用二次贝塞尔曲线绘制平滑圆角
this.pathGraphics.quadraticCurveTo(cornerPoint.x, cornerPoint.y, endPoint.x, endPoint.y);
```

#### 动画时序
```
0.0s: 路径开始绘制 (缩放0.8)
0.2s: 缩放到正常尺寸 (缩放1.0)
0.8s: 开始淡出
1.1s: 完全消失，自动清理
```

## 🎮 用户体验

### 游戏流程
1. **选择第一个棋子** → 棋子高亮显示
2. **选择第二个匹配棋子** → 检查连接性
3. **连接成功** → 显示连接路径
4. **路径动画** → 轻微缩放 + 保持显示
5. **棋子消除** → 棋子消失动画
6. **路径淡出** → 路径自动消失

### 视觉反馈
- ✅ **成功连接**: 显示彩色路径线条
- ✅ **路径清晰**: 线条宽度适中，易于识别
- ✅ **颜色协调**: 与棋子背景色保持一致
- ✅ **动画流畅**: 平滑的出现和消失效果

## 📊 性能优化

### 内存管理
- **及时清理**: 路径在不需要时立即清理
- **动画停止**: 销毁时停止所有相关动画
- **组件复用**: 路径渲染器组件复用，避免重复创建

### 渲染优化
- **独立节点**: 路径使用独立节点，不影响棋子渲染
- **合理尺寸**: UITransform设置为足够大的尺寸(2000x2000)
- **层级管理**: 路径节点在最上层显示，避免被遮挡

### 计算优化
- **坐标有效性检查**: 过滤无效坐标，避免渲染错误
- **路径点优化**: 只计算必要的路径点，减少绘制复杂度

## 🔧 API 接口

### 主要方法
```typescript
// 绘制连接路径
drawConnectionPath(startCell: Node, endCell: Node, pathPoints: Vec3[]): void

// 清除路径
clearPath(): void

// 设置线条样式
setLineStyle(width: number, opacity: number): void

// 设置动画时长
setAnimationDuration(duration: number): void

// 设置圆角半径
setCornerRadius(radius: number): void
```

### 配置建议
```typescript
// 标准配置
lineWidth: 4
animationDuration: 0.8
lineOpacity: 200
cornerRadius: 15

// 小屏幕设备
lineWidth: 3
cornerRadius: 10

// 大屏幕设备
lineWidth: 5
cornerRadius: 20
```

## 🔍 故障排除

### 常见问题
1. **路径不显示**: 检查UITransform组件和坐标转换
2. **颜色异常**: 检查棋子背景色配置
3. **动画卡顿**: 检查动画时长设置
4. **路径偏移**: 检查坐标系统和节点层级

### 调试方法
```typescript
// 临时启用调试日志
LogConfig.connectionSystem = true;
LogConfig.selectionLogic = true;
```

## 📁 文件结构

```
assets/scripts/
├── connectionPathRenderer.ts    # 路径渲染器组件
├── connectionSystem.ts         # 连接系统（扩展）
├── selectionLogicManager.ts    # 选择逻辑管理器（集成）
└── 连接路径功能说明.md        # 本说明文档
```

## 🚀 未来扩展

### 可能的增强功能
1. **路径动画**: 线条从起点到终点的绘制动画
2. **粒子效果**: 沿路径移动的粒子特效
3. **音效同步**: 路径绘制时的音效反馈
4. **主题样式**: 不同主题的路径外观
5. **自定义形状**: 支持更多的路径装饰效果

### 性能优化空间
1. **对象池**: 复用路径组件和Graphics对象
2. **批量绘制**: 多条路径的批量处理
3. **LOD系统**: 根据设备性能调整效果质量

## ✅ 总结

连接路径功能为连连看游戏提供了出色的视觉反馈，通过显示彩色的连接路径，让玩家能够清楚地看到棋子之间的连接关系。功能实现采用了模块化设计，具有良好的可扩展性和维护性，同时保持了与现有系统的完美兼容。

主要优势：
- 🎨 **视觉效果优雅**: 圆角转折和颜色匹配
- ⚡ **性能表现良好**: 优化的渲染和内存管理
- 🔧 **易于配置**: 丰富的可配置参数
- 🎮 **用户体验佳**: 流畅的动画和及时的反馈
