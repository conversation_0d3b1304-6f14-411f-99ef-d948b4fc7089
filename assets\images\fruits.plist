<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>icon_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,150}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{149,111},{112,150}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{132,140}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{1,211},{132,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{130,136}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{301,149},{130,136}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,148}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{1,347},{138,148}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,136}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{295,287},{144,136}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,142}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{151,347},{138,142}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{108,148}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{153,1},{108,148}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,146}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{303,1},{128,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{146,110}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{1,99},{146,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,150}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{143,225},{120,150}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,150}</string>
                <key>spriteSourceSize</key>
                <string>{150,150}</string>
                <key>textureRect</key>
                <string>{{1,1},{96,150}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>fruits.png</string>
            <key>size</key>
            <string>{432,486}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d0994b99232d0386fdfb45f2bb15ebc0:3ffbdb5d25778af93a6b7c01c17c93f0:56755924368b5e18801fdad7263bb90b$</string>
            <key>textureFileName</key>
            <string>fruits.png</string>
        </dict>
    </dict>
</plist>
