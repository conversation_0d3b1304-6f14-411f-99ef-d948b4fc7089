# 洗牌函数统一重构说明

## 🎯 重构目标

统一所有打乱/洗牌功能，让打乱按钮和自动重排都使用 `gameMgrSimplified` 中的同一个洗牌函数，确保行为一致性。

## 🔧 重构内容

### 1. **统一洗牌函数**

#### 核心洗牌函数位置：
```typescript
// assets/scripts/gameMgrSimplified.ts
public shuffleCells() {
    if (!this.gridNode || !this.gridNode.children) {
        console.warn("⚠️ 棋盘节点无效，无法执行洗牌");
        return;
    }

    const cellNodes = this.gridNode.children;

    // Fisher-Yates 洗牌算法
    for (let i = cellNodes.length - 1; i > 0; i--) {
        const randomIndex = Math.floor(Math.random() * (i + 1));
        const tempIcon = cellNodes[i].getComponent(cellFixed).getType();

        cellNodes[i].getComponent(cellFixed).setType(cellNodes[randomIndex].getComponent(cellFixed).getType());
        cellNodes[randomIndex].getComponent(cellFixed).setType(tempIcon);
    }

    console.log("🔀 棋盘已重新洗牌");
}
```

### 2. **无解检测器重构**

#### 修改前的问题：
- ❌ 使用回调函数，增加了复杂性
- ❌ 无法直接访问游戏管理器的其他功能
- ❌ 代码耦合度高，不易维护

#### 修改后的优势：
- ✅ 直接引用游戏管理器实例
- ✅ 可以调用游戏管理器的任何公共方法
- ✅ 代码更简洁，维护性更好

#### 重构对比：
```typescript
// 修改前：使用回调函数
public initialize(connectionSystem: ConnectionSystem, shuffleCallback: () => void, toastManager: simpleToast): void {
    this.connectionSystem = connectionSystem;
    this.shuffleCallback = shuffleCallback;  // 回调函数
    this.toastManager = toastManager;
}

// 执行打乱
this.shuffleCallback();

// 修改后：直接引用游戏管理器
public initialize(connectionSystem: ConnectionSystem, gameManager: any, toastManager: simpleToast): void {
    this.connectionSystem = connectionSystem;
    this.gameManager = gameManager;  // 游戏管理器实例
    this.toastManager = toastManager;
}

// 执行打乱
this.gameManager.shuffleCells();
```

### 3. **调用方式统一**

#### 所有洗牌调用都指向同一个函数：

1. **手动打乱按钮**：
```typescript
// menuManager.ts
private onShuffleClick() {
    console.log("🔀 点击打乱按钮");
    
    if (this.gameManager) {
        this.gameManager.shuffleCells();  // 调用统一洗牌函数
    }
}
```

2. **自动无解重排**：
```typescript
// deadlockDetector.ts
private performShuffle(reason: string): void {
    // 执行打乱 - 调用游戏管理器的洗牌函数
    this.gameManager.shuffleCells();  // 调用统一洗牌函数
}
```

3. **提示按钮无解处理**：
```typescript
// menuManager.ts
private onHintClick() {
    if (!hintShown) {
        // 触发无解检测和自动打乱
        this.gameManager['deadlockDetector'].forceShuffle();  // 最终也会调用统一洗牌函数
    }
}
```

## 🔄 系统架构优化

### 重构前的架构：
```
gameMgrSimplified.shuffleCells()  ← 游戏初始化时使用
        ↓
menuManager → shuffleCallback()   ← 打乱按钮使用回调
        ↓
deadlockDetector → shuffleCallback()  ← 自动重排使用回调
```

### 重构后的架构：
```
gameMgrSimplified.shuffleCells()  ← 唯一的洗牌函数
        ↑                ↑                ↑
menuManager        deadlockDetector    游戏初始化
(打乱按钮)         (自动重排)          (初始洗牌)
```

## 📊 重构优势

### 1. **代码一致性**
- ✅ 所有洗牌操作使用相同的算法
- ✅ 行为完全一致，避免差异
- ✅ 日志输出统一

### 2. **维护性提升**
- ✅ 只需维护一个洗牌函数
- ✅ 修改洗牌逻辑时只需改一处
- ✅ 减少代码重复

### 3. **扩展性增强**
- ✅ 无解检测器可以调用游戏管理器的其他方法
- ✅ 便于添加洗牌前后的额外逻辑
- ✅ 更容易集成新功能

### 4. **调试便利性**
- ✅ 统一的日志输出格式
- ✅ 更容易追踪洗牌操作的来源
- ✅ 便于性能监控和优化

## 🛠️ 技术实现细节

### 1. **初始化方式改变**
```typescript
// gameMgrSimplified.ts 中的初始化
// 修改前：
this.deadlockDetector.initialize(this.connectionSystem, () => {
    this.shuffleCells();
}, this.toastManager);

// 修改后：
this.deadlockDetector.initialize(this.connectionSystem, this, this.toastManager);
```

### 2. **类型安全性**
```typescript
// deadlockDetector.ts 中的类型检查
if (!this.gameManager || typeof this.gameManager.shuffleCells !== 'function') {
    console.error("❌ 游戏管理器或洗牌函数未设置");
    return;
}
```

### 3. **错误处理统一**
```typescript
// gameMgrSimplified.ts 中的错误处理
public shuffleCells() {
    if (!this.gridNode || !this.gridNode.children) {
        console.warn("⚠️ 棋盘节点无效，无法执行洗牌");
        return;
    }
    // ... 洗牌逻辑
}
```

## 🎮 用户体验保持

### 功能完全一致：
1. **打乱按钮** - 点击后立即洗牌，行为不变
2. **自动重排** - 无解时自动洗牌，行为不变
3. **提示按钮** - 无解时触发洗牌，行为不变

### 性能优化：
- 减少了函数调用层级
- 消除了回调函数的开销
- 统一的错误处理逻辑

## 📁 修改的文件

1. **`deadlockDetector.ts`**
   - 移除 `shuffleCallback` 回调函数
   - 添加 `gameManager` 引用
   - 更新 `initialize` 方法签名
   - 修改 `performShuffle` 方法实现

2. **`gameMgrSimplified.ts`**
   - 更新无解检测器的初始化调用
   - 保持 `shuffleCells` 方法不变

3. **`menuManager.ts`**
   - 确认打乱按钮已使用统一函数（无需修改）

## ✅ 验证方法

### 测试场景：
1. **手动打乱测试**：点击打乱按钮，观察洗牌效果
2. **自动重排测试**：创造无解状态，观察自动洗牌
3. **提示按钮测试**：在无解状态下点击提示按钮
4. **日志一致性**：检查所有洗牌操作的日志输出

### 预期结果：
- ✅ 所有洗牌操作行为完全一致
- ✅ 日志输出格式统一："🔀 棋盘已重新洗牌"
- ✅ 错误处理逻辑统一
- ✅ 性能表现良好

通过这次重构，系统现在具备了更好的一致性、维护性和扩展性，所有洗牌操作都使用同一个经过验证的算法！
