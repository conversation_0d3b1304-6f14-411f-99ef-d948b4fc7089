# 连接路径显示功能说明

## 🎯 功能概述

为连连看游戏添加了连接路径显示功能，当两个棋子成功连接消除时，会显示一条连接路径线条，颜色与棋子背景色一致，并自动消失。

## 🔧 技术实现

### 1. **ConnectionPathRenderer 组件**
- **文件**: `assets/scripts/connectionPathRenderer.ts`
- **功能**: 负责绘制和管理连接路径的显示
- **特性**:
  - 动态颜色匹配棋子背景色
  - 平滑的淡入淡出动画
  - 自动清理和内存管理

### 2. **ConnectionSystem 扩展**
- **文件**: `assets/scripts/connectionSystem.ts`
- **新增方法**: `getConnectionPath(cell1, cell2)`
- **功能**: 获取两个棋子之间的具体连接路径坐标点
- **支持路径类型**:
  - 直线连接（2个点）
  - 一个转弯连接（3个点）
  - 两个转弯连接（4个点）
  - 边界外连接（4个点）

### 3. **SelectionLogicManager 集成**
- **文件**: `assets/scripts/selectionLogicManager.ts`
- **集成点**: 在 `handleSuccessfulConnection` 方法中
- **时机**: 在播放音效之前，消除动画之前

## 📊 功能特性

### 🎨 **视觉效果**
- **颜色匹配**: 线条颜色自动匹配棋子背景色（稍微深一点）
- **线条样式**: 可配置的线条宽度和透明度
- **动画效果**: 淡入淡出效果，持续约0.8秒

### ⚙️ **可配置参数**
```typescript
@property({ type: CCFloat, displayName: "线条宽度" })
lineWidth: number = 4;

@property({ type: CCFloat, displayName: "动画持续时间" })
animationDuration: number = 0.8;

@property({ type: CCFloat, displayName: "线条透明度" })
lineOpacity: number = 200;
```

### 🔄 **路径类型支持**

#### 1. **直线连接**
```
A ——————— B
```
- 路径点：起点 → 终点

#### 2. **一个转弯连接**
```
A ———┐
     │
     └——— B
```
- 路径点：起点 → 转弯点 → 终点

#### 3. **两个转弯连接**
```
A ———┐
     │
     └———┐
         │
         └——— B
```
- 路径点：起点 → 转弯点1 → 转弯点2 → 终点

#### 4. **边界外连接**
```
A ———┐ (边界外)
     │
B ———┘
```
- 路径点：起点 → 边界外点1 → 边界外点2 → 终点

## 🚀 使用方法

### 自动集成
功能已自动集成到现有的游戏逻辑中，无需额外配置。当玩家成功连接两个棋子时，路径会自动显示。

### 手动调用（开发者）
```typescript
// 获取路径渲染器
const pathRenderer = gridNode.getComponent(ConnectionPathRenderer);

// 绘制自定义路径
const pathPoints = [startPos, cornerPos, endPos];
pathRenderer.drawConnectionPath(startCell, endCell, pathPoints);

// 清除路径
pathRenderer.clearPath();

// 配置样式
pathRenderer.setLineStyle(6, 180); // 宽度6，透明度180
pathRenderer.setAnimationDuration(1.0); // 动画1秒
```

## 📁 文件结构

```
assets/scripts/
├── connectionPathRenderer.ts     # 路径渲染器组件（新增）
├── connectionSystem.ts          # 连接系统（扩展）
├── selectionLogicManager.ts     # 选择逻辑管理器（修改）
└── 连接路径显示功能说明.md      # 本说明文档
```

## 🔍 技术细节

### 1. **坐标系统**
- **输入**: 世界坐标系的路径点
- **转换**: 自动转换为路径节点的本地坐标
- **绘制**: 使用Graphics组件绘制

### 2. **颜色计算**
```typescript
// 获取棋子背景色
const backgroundColor = cellComponent.backgroundColors[colorIndex];

// 生成线条颜色（稍微深一点）
const lineColor = new Color(
    Math.max(0, backgroundColor.r - 30),
    Math.max(0, backgroundColor.g - 30),
    Math.max(0, backgroundColor.b - 30),
    this.lineOpacity
);
```

### 3. **动画时序**
```
0.0s: 开始 (透明度0, 缩放0.8)
0.2s: 淡入完成 (透明度255, 缩放1.0)
0.6s: 保持显示
0.8s: 开始淡出
1.1s: 完全消失，清理资源
```

### 4. **内存管理**
- 自动清理Graphics绘制内容
- 停止所有相关动画
- 销毁时清理路径节点

## 🎮 游戏体验

### 玩家视角
1. **选择第一个棋子** → 棋子高亮
2. **选择第二个匹配棋子** → 显示连接路径
3. **路径动画播放** → 淡入效果
4. **棋子消除动画** → 路径保持显示
5. **路径自动消失** → 淡出效果

### 视觉反馈
- ✅ **成功连接**: 显示彩色路径线条
- ✅ **路径清晰**: 线条宽度适中，易于识别
- ✅ **颜色协调**: 与棋子背景色保持一致
- ✅ **动画流畅**: 平滑的淡入淡出效果

## 🔧 配置建议

### 不同屏幕尺寸
```typescript
// 小屏幕设备
lineWidth: 3
animationDuration: 0.6

// 大屏幕设备
lineWidth: 5
animationDuration: 1.0
```

### 不同游戏风格
```typescript
// 简约风格
lineOpacity: 150
lineWidth: 2

// 华丽风格
lineOpacity: 220
lineWidth: 6
```

## 🐛 故障排除

### 常见问题

#### 1. **路径不显示**
- 检查ConnectionPathRenderer组件是否正确添加到网格节点
- 确认getConnectionPath方法返回有效路径点

#### 2. **颜色不匹配**
- 验证cellFixed组件的backgroundColors数组
- 检查颜色索引计算逻辑

#### 3. **坐标偏移**
- 确认世界坐标到本地坐标的转换
- 检查路径节点的父子关系

#### 4. **动画异常**
- 确保UIOpacity组件存在
- 检查tween动画的目标对象

### 调试方法
```typescript
// 启用连接系统日志
LogConfig.connectionSystem = true;

// 启用选择逻辑日志
LogConfig.selectionLogic = true;

// 查看路径点信息
console.log("路径点:", pathPoints);
```

## 🚀 未来扩展

### 可能的增强功能
1. **路径动画效果**: 线条从起点到终点的绘制动画
2. **多样化线条样式**: 虚线、点线、波浪线等
3. **粒子效果**: 沿路径移动的粒子
4. **音效同步**: 路径绘制时的音效反馈
5. **自定义主题**: 不同主题的路径样式

### 性能优化
1. **对象池**: 复用路径节点和Graphics组件
2. **批量绘制**: 多条路径的批量处理
3. **LOD系统**: 根据距离调整路径细节

通过这个连接路径显示功能，玩家可以更清楚地看到棋子之间的连接关系，提升游戏的视觉反馈和用户体验！
