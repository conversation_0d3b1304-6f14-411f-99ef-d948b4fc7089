<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .config-item {
            margin: 5px 0;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 日志管理器测试</h1>
        
        <div class="config-section">
            <h3>📋 当前日志配置（增强版）</h3>
            <p style="color: #666; font-size: 14px; margin-bottom: 10px;">
                ✅ 启用关键业务日志：游戏管理器、菜单操作、音频状态、振动系统、界面切换<br>
                ✅ 保留重要提示：无解检测、Toast提示、错误信息<br>
                ❌ 禁用高频日志：连接系统、选择逻辑、动画状态等详细输出
            </p>
            <div id="config-display"></div>
            <button onclick="updateConfigDisplay()">刷新配置</button>
            <button onclick="enableDebugMode()">启用调试模式</button>
            <button onclick="enableProductionMode()">生产模式</button>
        </div>

        <div class="test-section">
            <h3>🎮 游戏管理器日志测试</h3>
            <button onclick="testGameManager()">测试游戏管理器日志</button>
        </div>

        <div class="test-section">
            <h3>🔗 连接系统日志测试</h3>
            <button onclick="testConnectionSystem()">测试连接系统日志</button>
        </div>

        <div class="test-section">
            <h3>👆 选择逻辑日志测试</h3>
            <button onclick="testSelectionLogic()">测试选择逻辑日志</button>
        </div>

        <div class="test-section">
            <h3>🎵 音频管理器日志测试</h3>
            <button onclick="testAudioManager()">测试音频管理器日志</button>
        </div>

        <div class="test-section">
            <h3>🔄 洗牌算法日志测试</h3>
            <button onclick="testShuffleAlgorithm()">测试洗牌算法日志</button>
        </div>

        <div class="test-section">
            <h3>🎬 动画状态日志测试</h3>
            <button onclick="testAnimationState()">测试动画状态日志</button>
        </div>

        <div class="test-section">
            <h3>📱 菜单管理器日志测试</h3>
            <button onclick="testMenuManager()">测试菜单管理器日志</button>
        </div>

        <div class="test-section">
            <h3>🍞 Toast系统日志测试</h3>
            <button onclick="testToastSystem()">测试Toast系统日志</button>
        </div>

        <div class="test-section">
            <h3>📦 加载界面日志测试</h3>
            <button onclick="testLoading()">测试加载界面日志</button>
        </div>

        <div class="test-section">
            <h3>🎯 棋子组件日志测试</h3>
            <button onclick="testCellComponent()">测试棋子组件日志</button>
        </div>

        <div class="test-section">
            <h3>🧪 测试模块日志测试</h3>
            <button onclick="testTestingModule()">测试测试模块日志</button>
        </div>

        <div class="test-section">
            <h3>📳 振动系统日志测试</h3>
            <button onclick="testVibration()">测试振动系统日志</button>
        </div>

        <div class="test-section">
            <h3>🔧 配置切换测试</h3>
            <button onclick="toggleAllLogs()">切换所有日志</button>
            <button onclick="toggleErrorLogs()">切换错误日志</button>
            <button onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <h3>📺 控制台输出</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <script>
        // 模拟LogConfig和LogManager（增强版配置）
        const LogConfig = {
            // 游戏核心模块 - 细化的游戏管理器日志开关
            gameManager: true,           // 游戏管理器总开关（启用，显示关键业务流程）
            gameManagerLifecycle: true,  // 生命周期日志（启动、进入游戏等）
            gameManagerSystem: true,     // 系统初始化日志（音频、振动、Toast等）
            gameManagerGrid: true,       // 棋盘相关日志（初始化、洗牌、创建等）
            gameManagerClick: false,     // 棋子点击日志（关闭，频繁输出）
            gameManagerWin: true,        // 游戏胜利日志（重要事件）
            gameManagerProgressive: false, // 渐进式展现日志（关闭，输出太多）
            gameManagerTest: false,      // 测试功能日志（仅开发时需要）

            connectionSystem: false,     // 连接系统日志（关闭，输出太多）
            selectionLogic: false,       // 选择逻辑日志（关闭，输出太多）
            animationState: false,       // 动画状态日志（关闭，输出太多）

            // 检测和算法模块 - 保留重要提示
            deadlockDetector: true,      // 无解检测日志（保留，重要提示）
            shuffleAlgorithm: false,     // 洗牌算法详细日志（关闭，太详细）

            // UI和交互模块 - 启用用户操作日志
            menuManager: true,           // 菜单管理器日志（启用，显示用户操作）
            toastSystem: true,           // Toast提示系统日志（保留，用户可见）

            // 音频模块 - 启用音频状态日志
            audioManager: false,         // 音频管理器日志（关闭）

            // 界面和组件模块 - 启用界面切换日志
            loading: false,              // 加载界面日志（关闭）
            cellComponent: false,        // 棋子组件日志（关闭，输出太多）

            // 系统模块 - 启用系统服务日志
            vibration: false,            // 振动系统日志（关闭）
            testing: false,              // 测试模块日志（仅开发时需要）

            // 全局开关
            enableAllLogs: false,        // 设置为 true 时启用所有日志
            enableErrorLogs: true,       // 错误日志始终启用（重要！）
        };

        const LogManager = {
            gameManager: {
                // 生命周期日志
                lifecycle: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerLifecycle)) {
                            logToConsole(`🚀 [GameManager-Lifecycle] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerLifecycle)) {
                            logToConsole(`🚀 [GameManager-Lifecycle] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🚀 [GameManager-Lifecycle] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 系统初始化日志
                system: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerSystem)) {
                            logToConsole(`🔧 [GameManager-System] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerSystem)) {
                            logToConsole(`🔧 [GameManager-System] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🔧 [GameManager-System] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 棋盘相关日志
                grid: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerGrid)) {
                            logToConsole(`🎯 [GameManager-Grid] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerGrid)) {
                            logToConsole(`🎯 [GameManager-Grid] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🎯 [GameManager-Grid] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 棋子点击日志
                click: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerClick)) {
                            logToConsole(`🖱️ [GameManager-Click] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerClick)) {
                            logToConsole(`🖱️ [GameManager-Click] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🖱️ [GameManager-Click] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 游戏胜利日志
                win: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerWin)) {
                            logToConsole(`🏆 [GameManager-Win] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerWin)) {
                            logToConsole(`🏆 [GameManager-Win] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🏆 [GameManager-Win] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 渐进式展现日志
                progressive: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerProgressive)) {
                            logToConsole(`🎬 [GameManager-Progressive] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerProgressive)) {
                            logToConsole(`🎬 [GameManager-Progressive] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🎬 [GameManager-Progressive] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 测试功能日志
                test: {
                    log: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerTest)) {
                            logToConsole(`🧪 [GameManager-Test] ${message}`, ...args);
                        }
                    },
                    warn: (message, ...args) => {
                        if (LogConfig.enableAllLogs || (LogConfig.gameManager && LogConfig.gameManagerTest)) {
                            logToConsole(`🧪 [GameManager-Test] WARNING: ${message}`, ...args);
                        }
                    },
                    error: (message, ...args) => {
                        if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                            logToConsole(`🧪 [GameManager-Test] ERROR: ${message}`, ...args);
                        }
                    }
                },
                // 保持向后兼容的原始方法
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                        logToConsole(`🎮 [GameManager] ${message}`, ...args);
                    }
                },
                warn: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                        logToConsole(`🎮 [GameManager] WARNING: ${message}`, ...args);
                    }
                },
                error: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                        logToConsole(`🎮 [GameManager] ERROR: ${message}`, ...args);
                    }
                }
            },
            connectionSystem: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.connectionSystem) {
                        logToConsole(`🔗 [Connection] ${message}`, ...args);
                    }
                }
            },
            selectionLogic: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.selectionLogic) {
                        logToConsole(`👆 [Selection] ${message}`, ...args);
                    }
                }
            },
            audioManager: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.audioManager) {
                        logToConsole(`🎵 [Audio] ${message}`, ...args);
                    }
                }
            },
            shuffleAlgorithm: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.shuffleAlgorithm) {
                        logToConsole(`🔄 [Shuffle] ${message}`, ...args);
                    }
                }
            },
            animationState: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.animationState) {
                        logToConsole(`🎬 [Animation] ${message}`, ...args);
                    }
                }
            },
            menuManager: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.menuManager) {
                        logToConsole(`📱 [Menu] ${message}`, ...args);
                    }
                }
            },
            toastSystem: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.toastSystem) {
                        logToConsole(`🍞 [Toast] ${message}`, ...args);
                    }
                }
            },
            loading: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.loading) {
                        logToConsole(`📦 [Loading] ${message}`, ...args);
                    }
                }
            },
            cellComponent: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.cellComponent) {
                        logToConsole(`🎯 [Cell] ${message}`, ...args);
                    }
                }
            },
            testing: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.testing) {
                        logToConsole(`🧪 [Testing] ${message}`, ...args);
                    }
                }
            },
            vibration: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.vibration) {
                        logToConsole(`📳 [Vibration] ${message}`, ...args);
                    }
                }
            }
        };

        function logToConsole(message, ...args) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = `[${timestamp}] ${message} ${args.length > 0 ? JSON.stringify(args) : ''}\n`;
            output.textContent += logLine;
            output.scrollTop = output.scrollHeight;
        }

        function updateConfigDisplay() {
            const display = document.getElementById('config-display');
            let html = '';
            for (const [key, value] of Object.entries(LogConfig)) {
                const status = value ? '✅' : '❌';
                html += `<div class="config-item">${status} ${key}: ${value}</div>`;
            }
            display.innerHTML = html;
        }

        function testGameManager() {
            // 测试生命周期日志
            LogManager.gameManager.lifecycle.log("[游戏启动] 开始初始化游戏管理器");
            LogManager.gameManager.lifecycle.log("[游戏进入] 进入游戏场景");

            // 测试系统初始化日志
            LogManager.gameManager.system.log("[音频系统] 音频管理器初始化完成");
            LogManager.gameManager.system.log("[振动系统] 振动系统初始化完成");
            LogManager.gameManager.system.log("[提示系统] Toast管理器初始化完成");

            // 测试棋盘相关日志
            LogManager.gameManager.grid.log("[棋盘初始化] 开始初始化游戏棋盘");
            LogManager.gameManager.grid.log("[棋子创建] 所有棋子创建完成");
            LogManager.gameManager.grid.log("[棋盘洗牌] 棋盘洗牌完成");

            // 测试点击日志（通常关闭）
            LogManager.gameManager.click.log("[棋子点击] 处理棋子点击: cell_2_3");

            // 测试胜利日志
            LogManager.gameManager.win.log("[游戏胜利] 开始执行胜利处理流程");
            LogManager.gameManager.win.log("[胜利检测] 当前剩余棋子数量: 0");

            // 测试渐进式展现日志（通常关闭）
            LogManager.gameManager.progressive.log("[渐进式展现] 开始渐进式创建 64 个棋子");
            LogManager.gameManager.progressive.log("[渐进式展现] 预计总展现时间: 3.20秒");

            // 测试功能日志（开发时使用）
            LogManager.gameManager.test.log("[功能测试] 开始测试渐进式展现功能");
            LogManager.gameManager.test.log("[配置测试] 基础间隔: 50ms");

            // 测试警告和错误
            LogManager.gameManager.grid.warn("[胜利检测] 棋盘节点不存在，跳过胜利检测");
            LogManager.gameManager.system.error("[系统初始化] 子系统初始化失败");
        }

        function testConnectionSystem() {
            LogManager.connectionSystem.log("连接系统测试");
            LogManager.connectionSystem.log("寻找连接路径");
            LogManager.connectionSystem.log("连接验证成功");
        }

        function testSelectionLogic() {
            LogManager.selectionLogic.log("选择逻辑测试");
            LogManager.selectionLogic.log("棋子选中");
            LogManager.selectionLogic.log("取消选中");
        }

        function testAudioManager() {
            LogManager.audioManager.log("🔧 [音频系统] 音频管理器开始初始化");
            LogManager.audioManager.log("✅ [音频系统] 音频源初始化完成");
            LogManager.audioManager.log("🔊 [音效播放] 音效播放成功: 索引0, 音量0.8");
            LogManager.audioManager.log("🔇 [背景音乐] 背景音乐已停止");
            LogManager.audioManager.warn("⚠️ [音效播放] 音频已禁用，跳过音效播放");
            LogManager.audioManager.error("❌ [音效播放] 音效播放失败: 索引99");
        }

        function testShuffleAlgorithm() {
            LogManager.shuffleAlgorithm.log("洗牌算法测试");
            LogManager.shuffleAlgorithm.log("开始洗牌");
            LogManager.shuffleAlgorithm.log("洗牌完成");
        }

        function testAnimationState() {
            LogManager.animationState.log("动画状态测试");
            LogManager.animationState.log("动画开始");
            LogManager.animationState.log("动画结束");
        }

        function testMenuManager() {
            LogManager.menuManager.log("🔀 [菜单操作] 用户点击打乱按钮");
            LogManager.menuManager.log("✅ [菜单操作] 棋盘打乱操作执行完成");
            LogManager.menuManager.log("🔄 [菜单操作] 用户点击重新开始按钮");
            LogManager.menuManager.log("✅ [菜单操作] 游戏重新开始操作执行完成");
            LogManager.menuManager.error("❌ [菜单操作] 游戏管理器未找到，无法执行打乱");
        }

        function testToastSystem() {
            LogManager.toastSystem.log("Toast系统测试");
            LogManager.toastSystem.log("显示提示");
            LogManager.toastSystem.log("隐藏提示");
        }

        function testLoading() {
            LogManager.loading.log("🚀 [界面切换] 用户点击开始游戏按钮");
            LogManager.loading.log("🔊 [音效播放] 点击音效播放完成");
            LogManager.loading.log("🔇 [音乐控制] Loading音乐已停止");
            LogManager.loading.log("📳 [触觉反馈] 振动反馈已触发");
            LogManager.loading.log("🔄 [界面切换] 开始执行优化的界面切换流程");
            LogManager.loading.log("🎵 [音频切换] 游戏音频切换完成");
            LogManager.loading.log("🎮 [游戏加载] 游戏界面加载完成");
            LogManager.loading.log("✅ [界面切换] Loading界面切换流程完全完成");
        }

        function testCellComponent() {
            LogManager.cellComponent.log("棋子组件测试");
            LogManager.cellComponent.log("棋子创建");
            LogManager.cellComponent.log("棋子销毁");
        }

        function testTestingModule() {
            LogManager.testing.log("测试模块测试");
            LogManager.testing.log("开始测试");
            LogManager.testing.log("测试完成");
        }

        function testVibration() {
            LogManager.vibration.log("📳 [振动系统] 开始播放振动效果: [100, 50, 100]");
            LogManager.vibration.log("✅ [振动系统] 振动效果已成功触发");
            LogManager.vibration.warn("⚠️ [振动系统] 振动已被用户禁用");
            LogManager.vibration.warn("⚠️ [振动系统] 设备不支持振动，使用视觉效果降级");
            LogManager.vibration.error("❌ [振动系统] 振动API调用失败");
        }

        function toggleAllLogs() {
            LogConfig.enableAllLogs = !LogConfig.enableAllLogs;
            logToConsole(`🔧 所有日志已${LogConfig.enableAllLogs ? '启用' : '禁用'}`);
            updateConfigDisplay();
        }

        function toggleErrorLogs() {
            LogConfig.enableErrorLogs = !LogConfig.enableErrorLogs;
            logToConsole(`🔧 错误日志已${LogConfig.enableErrorLogs ? '启用' : '禁用'}`);
            updateConfigDisplay();
        }

        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }

        function enableDebugMode() {
            // 启用所有日志用于调试
            Object.keys(LogConfig).forEach(key => {
                if (key !== 'enableAllLogs' && key !== 'enableErrorLogs') {
                    LogConfig[key] = true;
                }
            });
            LogConfig.enableAllLogs = true;
            logToConsole('🔧 已切换到调试模式 - 启用所有日志');
            updateConfigDisplay();
        }

        function enableProductionMode() {
            // 只保留错误日志
            Object.keys(LogConfig).forEach(key => {
                if (key !== 'enableErrorLogs') {
                    LogConfig[key] = false;
                }
            });
            LogConfig.enableErrorLogs = true;
            logToConsole('🔧 已切换到生产模式 - 只保留错误日志');
            updateConfigDisplay();
        }

        // 初始化显示
        updateConfigDisplay();
        logToConsole('🚀 日志管理器测试页面已加载（增强配置）');
        logToConsole('💡 当前启用关键业务日志：游戏管理器、菜单操作、音频状态、振动系统、界面切换');
        logToConsole('💡 保留重要提示：无解检测、Toast提示、错误信息');
        logToConsole('💡 禁用高频日志：连接系统、选择逻辑、动画状态等详细输出');
    </script>
</body>
</html>
