import { _decorator, Component, Node, Graphics, Color, Vec3, tween, UITransform, UIOpacity, CCFloat } from 'cc';
const { ccclass, property } = _decorator;
import { cellFixed } from './cellFixed';

/**
 * 连接路径渲染器
 * 负责绘制连接两个棋子的路径线条
 */
@ccclass('ConnectionPathRenderer')
export class ConnectionPathRenderer extends Component {
    
    @property({ type: CCFloat, displayName: "线条宽度" })
    lineWidth: number = 6;
    
    @property({ type: CCFloat, displayName: "动画持续时间" })
    animationDuration: number = 0.05;
    
    @property({ type: CCFloat, displayName: "线条透明度" })
    lineOpacity: number = 200;

    @property({ type: CCFloat, displayName: "圆角半径" })
    cornerRadius: number = 15;
    
    // 当前绘制的路径节点
    private pathNode: Node = null;
    private pathGraphics: Graphics = null;
    
    onLoad() {
        // 创建专门用于绘制路径的节点
        this.createPathNode();
    }
    
    /**
     * 创建路径绘制节点
     */
    private createPathNode(): void {
        this.pathNode = new Node('ConnectionPath');
        this.pathGraphics = this.pathNode.addComponent(Graphics);

        // 添加UIOpacity组件用于透明度动画
        if (!this.pathNode.getComponent(UIOpacity)) {
            this.pathNode.addComponent(UIOpacity);
        }

        // 检查UITransform组件是否已存在，如果不存在才添加
        let uiTransform = this.pathNode.getComponent(UITransform);
        if (!uiTransform) {
            uiTransform = this.pathNode.addComponent(UITransform);
        }

        // 设置足够大的尺寸以容纳路径绘制
        uiTransform.setContentSize(2000, 2000);
        uiTransform.setAnchorPoint(0.5, 0.5); // 中心锚点

        // 添加一个特殊标记，表示这不是游戏棋子
        this.pathNode['isPathRenderer'] = true;

        // 设置路径节点的层级和位置
        this.pathNode.layer = this.node.layer; // 使用相同的渲染层
        this.pathNode.position = new Vec3(0, 0, 0); // 重置位置

        // 添加到父节点
        this.node.addChild(this.pathNode);

        // 设置路径节点在最上层显示
        this.pathNode.setSiblingIndex(999);


    }
    
    /**
     * 绘制连接路径
     * @param startCell 起始棋子
     * @param endCell 结束棋子
     * @param pathPoints 路径关键点（世界坐标）
     */
    public drawConnectionPath(startCell: Node, endCell: Node, pathPoints: Vec3[]): void {
        if (!this.pathGraphics || !startCell || !endCell) {
            return;
        }

        if (!pathPoints || pathPoints.length < 2) {
            return;
        }

        // 获取棋子的背景颜色
        const lineColor = this.getCellBackgroundColor(startCell);

        // 清除之前的路径
        this.clearPath();

        // 设置线条样式
        this.pathGraphics.lineWidth = this.lineWidth;
        this.pathGraphics.strokeColor = lineColor;
        this.pathGraphics.fillColor = new Color(0, 0, 0, 0); // 透明填充

        // 绘制路径
        this.drawPath(pathPoints);

        // 播放路径动画
        this.playPathAnimation();
    }
    
    /**
     * 获取棋子的背景颜色
     */
    private getCellBackgroundColor(cellNode: Node): Color {
        const cellComponent = cellNode.getComponent(cellFixed);
        if (!cellComponent) {
            return new Color(100, 100, 100, this.lineOpacity); // 默认灰色
        }

        const cellType = cellComponent.getType();
        const backgroundColors = cellComponent.backgroundColors;

        // 使用简化的颜色映射
        const colorIndex = cellType % backgroundColors.length;
        const backgroundColor = backgroundColors[colorIndex] || backgroundColors[0];

        // 创建稍微深一点的颜色作为线条颜色，并设置透明度
        return new Color(
            Math.max(0, backgroundColor.r - 30),
            Math.max(0, backgroundColor.g - 30),
            Math.max(0, backgroundColor.b - 30),
            this.lineOpacity
        );
    }
    
    /**
     * 绘制路径线条（带圆角转折）
     */
    private drawPath(pathPoints: Vec3[]): void {
        if (pathPoints.length < 2) {
            console.warn("路径点数量不足，无法绘制");
            return;
        }

        // 转换为路径节点的本地坐标
        const localPoints = pathPoints.map(worldPos => {
            const gridTransform = this.pathNode.parent?.getComponent(UITransform);
            if (gridTransform) {
                return gridTransform.convertToNodeSpaceAR(worldPos);
            } else {
                return worldPos;
            }
        });

        // 检查坐标有效性
        const validPoints = localPoints.filter(point => {
            return !isNaN(point.x) && !isNaN(point.y) &&
                   Math.abs(point.x) < 10000 && Math.abs(point.y) < 10000;
        });

        if (validPoints.length < 2) {
            return;
        }

        // 开始新的路径
        this.pathGraphics.clear();

        if (validPoints.length === 2) {
            // 直线连接，无需圆角
            this.drawStraightPath(validPoints);
        } else {
            // 多点连接，使用圆角转折
            this.drawRoundedPath(validPoints);
        }

        // 执行绘制
        this.pathGraphics.stroke();

        // 强制刷新渲染
        this.pathGraphics.markForUpdateRenderData();
    }

    /**
     * 绘制直线路径
     */
    private drawStraightPath(points: Vec3[]): void {
        this.pathGraphics.moveTo(points[0].x, points[0].y);
        this.pathGraphics.lineTo(points[1].x, points[1].y);
    }

    /**
     * 绘制圆角路径
     */
    private drawRoundedPath(points: Vec3[]): void {
        if (points.length < 3) {
            this.drawStraightPath(points);
            return;
        }

        // 从第一个点开始
        this.pathGraphics.moveTo(points[0].x, points[0].y);

        for (let i = 1; i < points.length - 1; i++) {
            const prevPoint = points[i - 1];
            const currentPoint = points[i];
            const nextPoint = points[i + 1];

            // 计算到转折点的距离
            const distToPrev = this.getDistance(currentPoint, prevPoint);
            const distToNext = this.getDistance(currentPoint, nextPoint);

            // 圆角半径不能超过线段长度的一半
            const maxRadius = Math.min(distToPrev, distToNext) * 0.4;
            const radius = Math.min(this.cornerRadius, maxRadius);

            if (radius > 0) {
                // 计算圆角的起始和结束点
                const startPoint = this.getPointOnLine(currentPoint, prevPoint, radius);
                const endPoint = this.getPointOnLine(currentPoint, nextPoint, radius);

                // 绘制到圆角起始点的直线
                this.pathGraphics.lineTo(startPoint.x, startPoint.y);

                // 绘制圆角
                this.drawRoundedCorner(startPoint, currentPoint, endPoint, radius);
            } else {
                // 如果半径太小，直接绘制直线
                this.pathGraphics.lineTo(currentPoint.x, currentPoint.y);
            }
        }

        // 绘制到最后一个点
        const lastPoint = points[points.length - 1];
        this.pathGraphics.lineTo(lastPoint.x, lastPoint.y);
    }

    /**
     * 绘制圆角转折
     */
    private drawRoundedCorner(_startPoint: Vec3, cornerPoint: Vec3, endPoint: Vec3, _radius: number): void {
        // 使用quadraticCurveTo绘制平滑的圆角曲线
        // 控制点就是原来的转折点，这样可以创建自然的圆角效果
        this.pathGraphics.quadraticCurveTo(cornerPoint.x, cornerPoint.y, endPoint.x, endPoint.y);
    }

    /**
     * 计算两点之间的距离
     */
    private getDistance(point1: Vec3, point2: Vec3): number {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 在线段上获取距离指定点特定距离的点
     */
    private getPointOnLine(fromPoint: Vec3, toPoint: Vec3, distance: number): Vec3 {
        const totalDistance = this.getDistance(fromPoint, toPoint);
        if (totalDistance === 0) return fromPoint;

        const ratio = distance / totalDistance;
        return new Vec3(
            fromPoint.x + (toPoint.x - fromPoint.x) * ratio,
            fromPoint.y + (toPoint.y - fromPoint.y) * ratio,
            0
        );
    }
    
    /**
     * 播放路径动画（淡入淡出效果）
     */
    private playPathAnimation(): void {
        if (!this.pathNode) {
            return;
        }

        const uiOpacity = this.pathNode.getComponent(UIOpacity);
        if (!uiOpacity) {
            return;
        }

        // 停止之前的动画
        tween(this.pathNode).stop();
        tween(uiOpacity).stop();

        // 初始状态：稍微缩小，然后放大
        this.pathNode.setScale(0.8, 0.8, 1);
        uiOpacity.opacity = 255;

        // 缩放动画：放大到正常尺寸
        tween(this.pathNode)
            .to(0.05, { scale: new Vec3(1, 1, 1) })
            .start();

        // 透明度动画：保持显示后淡出
        tween(uiOpacity)
            .delay(this.animationDuration) // 保持显示
            .to(0.3, { opacity: 0 }) // 淡出
            .call(() => {
                this.clearPath();
            })
            .start();
    }
    
    /**
     * 清除当前路径
     */
    public clearPath(): void {
        if (this.pathGraphics) {
            this.pathGraphics.clear();
        }

        // 停止所有动画
        if (this.pathNode) {
            tween(this.pathNode).stop();
            const uiOpacity = this.pathNode.getComponent(UIOpacity);
            if (uiOpacity) {
                tween(uiOpacity).stop();
                uiOpacity.opacity = 0; // 确保透明度重置
            }
        }


    }
    
    /**
     * 设置线条样式
     */
    public setLineStyle(width: number, opacity: number): void {
        this.lineWidth = width;
        this.lineOpacity = opacity;
    }
    
    /**
     * 设置动画持续时间
     */
    public setAnimationDuration(duration: number): void {
        this.animationDuration = duration;
    }

    /**
     * 设置圆角半径
     */
    public setCornerRadius(radius: number): void {
        this.cornerRadius = Math.max(0, radius);
    }



    /**
     * 测试真实棋子坐标的路径绘制
     */
    public drawRealCellPath(cell1: Node, cell2: Node): void {
        if (!this.pathGraphics || !cell1 || !cell2) {
            console.error("参数无效，无法绘制真实棋子路径");
            return;
        }

        // 获取棋子的世界坐标
        const pos1 = cell1.getWorldPosition();
        const pos2 = cell2.getWorldPosition();

        // 清除之前的路径
        this.pathGraphics.clear();

        // 设置明显的样式
        this.pathGraphics.lineWidth = 8;
        this.pathGraphics.strokeColor = new Color(255, 255, 0, 255); // 黄色

        // 转换坐标
        const gridTransform = this.pathNode.parent?.getComponent(UITransform);
        if (gridTransform) {
            const localPos1 = gridTransform.convertToNodeSpaceAR(pos1);
            const localPos2 = gridTransform.convertToNodeSpaceAR(pos2);

            // 绘制直线
            this.pathGraphics.moveTo(localPos1.x, localPos1.y);
            this.pathGraphics.lineTo(localPos2.x, localPos2.y);
            this.pathGraphics.stroke();

            // 在两端绘制小圆圈标记
            this.pathGraphics.circle(localPos1.x, localPos1.y, 10);
            this.pathGraphics.circle(localPos2.x, localPos2.y, 10);
            this.pathGraphics.fillColor = new Color(255, 255, 0, 150);
            this.pathGraphics.fill();
        } else {
            console.error("无法获取网格节点UITransform");
        }
    }

    /**
     * 绘制持久路径（不会被动画清除）
     */
    public drawPersistentPath(cell1: Node, cell2: Node): void {
        if (!this.pathGraphics || !cell1 || !cell2) {
            console.error("参数无效，无法绘制持久路径");
            return;
        }

        // 获取棋子的世界坐标
        const pos1 = cell1.getWorldPosition();
        const pos2 = cell2.getWorldPosition();

        // 清除之前的路径
        this.pathGraphics.clear();

        // 设置明显的样式
        this.pathGraphics.lineWidth = 6;
        this.pathGraphics.strokeColor = new Color(0, 255, 255, 255); // 青色

        // 转换坐标并绘制
        const gridTransform = this.pathNode.parent?.getComponent(UITransform);
        if (gridTransform) {
            const localPos1 = gridTransform.convertToNodeSpaceAR(pos1);
            const localPos2 = gridTransform.convertToNodeSpaceAR(pos2);



            // 绘制直线
            this.pathGraphics.moveTo(localPos1.x, localPos1.y);
            this.pathGraphics.lineTo(localPos2.x, localPos2.y);
            this.pathGraphics.stroke();

            // 确保完全可见
            this.pathNode.active = true;
            const uiOpacity = this.pathNode.getComponent(UIOpacity);
            if (uiOpacity) {
                uiOpacity.opacity = 255;
            }

            // 停止所有动画，防止被清除
            tween(this.pathNode).stop();
            tween(uiOpacity).stop();


        }
    }
    


    onDestroy() {
        this.clearPath();
        if (this.pathNode) {
            this.pathNode.destroy();
        }
    }
}
